'use client';

import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getOrderProposals } from '@/api/proposal-api';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { formatProposalTime, roundToThreeDecimals } from '@/lib/utils';
import type {
  OrderEntity,
  ProposalEntity,
  UserType,
} from '@/marketplace-shared';
import { ProposalStatus } from '@/marketplace-shared';
import { useRootContext } from '@/root-context';
import {
  getProposalStatusColor,
  getProposalStatusMessageKey,
  isUserProposer,
  isUserSeller,
} from '@/services/proposal-service';
import { globalCache } from '@/utils/cache-utils';

import { proposalsTableMessages } from './intl/proposals-table.messages';

// Cache configuration for proposals data
const PROPOSALS_CACHE_CONFIG = { duration: 5 * 60 * 1000 }; // 5 minutes

// Helper functions to generate cache keys
function getProposalsCacheKey(orderId: string): string {
  return `order-proposals:${orderId}`;
}

interface ProposalsTableProps {
  order: OrderEntity;
  userType?: UserType;
  onAcceptProposal?: (proposalId: string) => void;
  onCancelProposal?: (proposalId: string) => void;
  acceptingProposalId?: string;
  cancellingProposal?: boolean;
  onRefreshProposals?: () => void;
}

export function ProposalsTable({
  order,
  userType,
  onAcceptProposal,
  onCancelProposal,
  acceptingProposalId,
  cancellingProposal,
  onRefreshProposals,
}: ProposalsTableProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();

  const [proposals, setProposals] = useState<ProposalEntity[]>([]);
  const [loading, setLoading] = useState(true);

  const loadProposals = async () => {
    if (!order.id) return;

    const cacheKey = getProposalsCacheKey(order.id);

    // Check cache first
    const cachedProposals = globalCache.get<ProposalEntity[]>(cacheKey);
    if (cachedProposals) {
      setProposals(cachedProposals);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // Load ALL proposals at once (no lazy loading)
      const result = await getOrderProposals(order.id);
      setProposals(result);

      // Cache the proposals data
      globalCache.set(cacheKey, result, PROPOSALS_CACHE_CONFIG);
    } catch (error) {
      console.error('Error loading proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (order.id) {
      loadProposals();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [order.id]);

  // Expose refresh function to parent
  useEffect(() => {
    if (onRefreshProposals) {
      onRefreshProposals();
    }
  }, [proposals, onRefreshProposals]);

  const handleAcceptProposal = (proposalId: string) => {
    if (onAcceptProposal) {
      onAcceptProposal(proposalId);
    }
  };

  const getProposalStatusText = (status: ProposalStatus) => {
    const messageKey = getProposalStatusMessageKey(status);
    return typeof messageKey === 'string' ? messageKey : t(messageKey);
  };

  const isSeller = userType === 'seller' || isUserSeller(order, currentUser);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
      </div>
    );
  }

  if (proposals.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400">
        {t(proposalsTableMessages.noProposals)}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {proposals.length > 0 && (
        <div className="space-y-3">
          {proposals.map((proposal) => {
            const isCurrentUserSeller = isSeller;
            const isCurrentUserProposer = isUserProposer(proposal, currentUser);

            const shouldShowAcceptButton =
              isCurrentUserSeller &&
              !isCurrentUserProposer &&
              proposal.status === ProposalStatus.ACTIVE;

            const shouldShowCancelButton =
              isCurrentUserProposer &&
              proposal.status === ProposalStatus.ACTIVE;

            return (
              <div
                key={proposal.id}
                className="bg-card rounded-lg p-4 border border-border"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <span className="text-foreground font-bold">
                        {roundToThreeDecimals(proposal.proposed_price)}
                      </span>
                      <TonLogo size={16} />
                    </div>
                    <span
                      className={`text-sm ${getProposalStatusColor(proposal.status)}`}
                    >
                      {getProposalStatusText(proposal.status)}
                    </span>
                    {proposal.createdAt && (
                      <span className="text-xs text-muted-foreground">
                        {formatProposalTime(proposal.createdAt)}
                      </span>
                    )}
                  </div>

                  {shouldShowAcceptButton && (
                    <Button
                      onClick={() => handleAcceptProposal(proposal.id!)}
                      disabled={!!acceptingProposalId}
                      size="sm"
                      className="bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white"
                    >
                      {acceptingProposalId === proposal.id ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin mr-1" />
                          {t(proposalsTableMessages.accepting)}
                        </>
                      ) : (
                        t(proposalsTableMessages.accept)
                      )}
                    </Button>
                  )}

                  {shouldShowCancelButton && (
                    <Button
                      onClick={() =>
                        onCancelProposal && onCancelProposal(proposal.id!)
                      }
                      disabled={cancellingProposal}
                      variant="outline"
                      size="sm"
                      className="border-red-500 text-red-500 hover:bg-red-500/10 cursor-pointer"
                    >
                      {cancellingProposal ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin mr-1" />
                          {t(proposalsTableMessages.cancelling)}
                        </>
                      ) : (
                        t(proposalsTableMessages.cancel)
                      )}
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
