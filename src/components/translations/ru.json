{"actions.apply": "Применить", "actions.buy": "Купить", "actions.cancel": "Отмена", "actions.close": "Закрыть", "actions.confirm": "Подтвердить", "actions.delete": "Удалить", "actions.edit": "Редактировать", "actions.fulfill": "Исполнить", "actions.pause": "Пауза", "actions.reject": "Отклонить", "actions.resend": "Отправить повторно", "actions.save": "Сохранить", "actions.saved": "Сохранено", "actions.send": "Отправить", "actions.sent": "Отправлено", "actions.signIn": "Войти", "actions.signOut": "Выйти", "actions.signUp": "Зарегистрироваться", "actions.submit": "Отправить", "attachGiftToOrderDrawer.activateOrderTitle": "Активировать заказ", "attachGiftToOrderDrawer.activateStep1": "1. Отправьте подарок напрямую релейеру", "attachGiftToOrderDrawer.activateStep2": "2. Вернитесь в приложение", "attachGiftToOrderDrawer.activateStep3": "3. Нажмите кнопку 'Привязать подарок'", "attachGiftToOrderDrawer.activateStep4": "4. Выберите соответствующий подарок", "attachGiftToOrderDrawer.activateStep5": "5. Подтвердите привязку", "attachGiftToOrderDrawer.errorLinkingGift": "Ошибка привязки подарка к заказу", "attachGiftToOrderDrawer.errorLoadingGifts": "Ошибка загрузки доступных подарков", "attachGiftToOrderDrawer.giftLinkedSuccessfully": "Подарок успешно привязан к заказу", "attachGiftToOrderDrawer.instructionStep1": "Отправьте подарок напрямую релейеру, затем вернитесь в приложение и привяжите соответствующий подарок к вашему заказу", "attachGiftToOrderDrawer.instructionStep2": "2. Нажмите кнопку \"Привязать подарок к этому заказу\" и выберите внесённый подарок", "attachGiftToOrderDrawer.instructionsTitle": "Как привязать подарок:", "attachGiftToOrderDrawer.linkGiftToOrder": "Привязать подарок к заказу", "attachGiftToOrderDrawer.linking": "Привязка...", "attachGiftToOrderDrawer.noGiftsAvailable": "Нет доступных подарков для этой коллекции", "attachGiftToOrderDrawer.selectGift": "Выберите подарок для привязки", "attachGiftToOrderDrawer.title": "Привязать подарок к заказу", "collection.status.deleted": "Удалено", "collection.status.market": "Рынок", "collection.status.premarket": "Предпродажа", "collectionName.unknownCollection": "Неизвестная коллекция", "collectionSelect.collection": "Коллекция", "collectionSelect.noCollectionsFound": "Коллекции, соответствующие \"{searchQuery}\", не найдены.", "collectionSelect.searchCollections": "Поиск коллекций...", "collectionSelect.selectCollection": "Выберите коллекцию...", "common.failedToShare": "Не удалось поделиться", "common.failedToShareReferralLink": "Не удалось поделиться реферальной ссылкой", "common.linkCopiedToClipboard": "Ссылка скопирована в буфер обмена", "common.referralLinkCopiedToClipboard": "Реферальная ссылка скопирована в буфер обмена", "confirmWrapper.defaultMessage": "Вы уверены, что хотите продолжить?", "confirmWrapper.no": "Нет", "confirmWrapper.yes": "Да", "countdownPopup.closeNotification": "Закрыть уведомление", "countdownPopup.depositProcessing": "Обработка депозита", "countdownPopup.minutes": "минут", "countdownPopup.youWillReceiveFundsWithin": "Вы получите средства в течение", "createSellOrderDrawer.cancel": "Отмена", "createSellOrderDrawer.createOrder": "Создать заказ", "createSellOrderDrawer.createSellOrder": "Создать заказ на продажу", "createSellOrderDrawer.creating": "Создание...", "createSellOrderDrawer.priceLabel": "Цена (TON)", "createSellOrderDrawer.setPrice": "Установите цену для вашего подарка {giftName}", "createSellOrderDrawer.successMessage": "Заказ на продажу успешно создан!", "depositDrawer.actions.cancel": "Отмена", "depositDrawer.actions.deposit": "Депозит", "depositDrawer.actions.pleaseConnectWallet": "Пожалуйста, подключите кошелек для внесения депозита", "depositDrawer.actions.processing": "Обработка...", "depositDrawer.addTonToBalance": "Добавить TON на баланс маркетплейса", "depositDrawer.amountInput.amountMustBeAtLeast": "Сумма должна быть не менее {amount} TON", "depositDrawer.amountInput.depositAmountTon": "Сумма депозита (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Мин {amount} TON", "depositDrawer.close": "Закрыть", "depositDrawer.copyTransactionHash": "Скопировать хеш транзакции", "depositDrawer.depositCompleted": "Ваш депозит успешно завершён", "depositDrawer.depositFee": "Комиссия депозита:", "depositDrawer.depositFunds": "Внести средства", "depositDrawer.depositInformation": "Информация о депозите", "depositDrawer.depositProcessing": "Обработка депозита", "depositDrawer.depositSuccess": "Депозит успешен!", "depositDrawer.loadingConfiguration": "Загрузка конфигурации...", "depositDrawer.minimumDeposit": "Минимальный депозит:", "depositDrawer.processingYourDeposit": "Обработка вашего депозита...", "depositDrawer.summary.depositAmount": "Сумма депозита:", "depositDrawer.summary.depositFee": "Комиссия депозита:", "depositDrawer.summary.totalToPay": "Всего к оплате:", "depositDrawer.transactionHashCopied": "Хеш транзакции скопирован в буфер обмена", "depositDrawer.viewOnTonScan": "Посмотреть в TON Scan", "depositDrawer.youWillReceiveFundsWithin": "Вы получите средства в течение", "errorPage.tryAgain": "Попробовать снова", "errorPage.unhandledErrorOccurred": "Произошла необработанная ошибка!", "errors.auth.adminOnly": "Только администраторы могут выполнить эту операцию.", "errors.auth.permissionDenied": "Доступ запрещен.", "errors.auth.permissionDeniedWithOperation": "Вы можете выполнить {operation} только для себя.", "errors.auth.telegramIdRequired": "Требуется Telegram ID.", "errors.auth.tonWalletRequired": "У пользователя не настроен адрес кошелька TON.", "errors.auth.unauthenticated": "Требуется аутентификация.", "errors.auth.userNotFound": "Пользователь не найден.", "errors.auth.walletAlreadyUsed": "Этот адрес кошелька уже используется другим пользователем.", "errors.balance.insufficientLockedFunds": "Недостаточно заблокированных средств", "errors.balance.insufficientLockedFundsToSpend": "Недостаточно заблокированных средств для трат", "errors.balance.insufficientLockedFundsToUnlock": "Недостаточно заблокированных средств для разблокировки", "errors.fulfillAndResell.insufficientBalance": "Недостаточно средств для создания заказа на перепродажу.", "errors.fulfillAndResell.invalidOrderStatus": "Заказ должен иметь статус \"подарок отправлен релейеру\" для перепродажи.", "errors.fulfillAndResell.invalidParameters": "ID заказа и цена перепродажи обязательны и должны быть действительными.", "errors.fulfillAndResell.notOrderBuyer": "Вы не являетесь покупателем этого заказа.", "errors.fulfillAndResell.orderNotFound": "Заказ не найден.", "errors.generic.authenticationFailed": "Ошибка аутентификации.", "errors.generic.operationFailed": "Операция не удалась. Попробуйте снова.", "errors.generic.serverError": "Произошла ошибка сервера.", "errors.generic.unknownError": "Произошла неизвестная ошибка.", "errors.gift.giftAlreadyLinked": "Подарок уже привязан к заказу", "errors.gift.giftCollectionMismatch": "Подарок не соответствует коллекции заказа", "errors.gift.giftInvalidStatus": "Неверный статус подарка", "errors.gift.giftNotFound": "Подарок не найден", "errors.gift.giftNotOwnedByUser": "Подарок не принадлежит пользователю", "errors.order.buyerCannotPurchaseSameOrder": "Вы не можете купить тот же заказ снова.", "errors.order.buyersCannotCreateMarketOrders": "Покупатели не могут создавать заказы для рыночных коллекций. Только продавцы могут создавать заказы для рыночных коллекций.", "errors.order.collectionNotActive": "Коллекция не активна.", "errors.order.collectionNotFound": "Коллекция не найдена.", "errors.order.insufficientBalance": "Недостаточно средств.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Только текущий покупатель может установить цену на вторичном рынке.", "errors.order.onlyPaidOrdersPurchasable": "На вторичном рынке можно покупать только заказы со статусом ОПЛАЧЕН.", "errors.order.onlyPaidOrdersSecondaryMarket": "На вторичном рынке можно размещать только заказы со статусом ОПЛАЧЕН.", "errors.order.orderMustBeGiftSentStatus": "Заказ должен иметь статус 'подарок отправлен релейеру' для завершения покупки.", "errors.order.orderMustBePaidStatus": "Заказ должен иметь статус 'оплачен' для отправки подарка релейеру.", "errors.order.orderMustHaveBuyerAndSeller": "У заказа должны быть и покупатель, и продавец для размещения на вторичном рынке.", "errors.order.orderNotAvailableSecondaryMarket": "Заказ недоступен на вторичном рынке.", "errors.order.orderNotFound": "Заказ не найден.", "errors.order.secondaryPriceBelowMinimum": "Цена на вторичном рынке должна быть не менее {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Цена на вторичном рынке не может превышать общий залог {totalCollateral} TON (покупатель: {buyerAmount} TON + продавец: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Продавеац не может купить свой собственный заказ на вторичном рынке.", "errors.order.tooManyCreatedOrders": "У вас уже есть 3 заказа, которые нужно активировать. Пожалуйста, активируйте существующие заказы перед созданием новых.", "errors.proposal.cannotProposeOnSecondaryMarket": "Нельзя делать предложения на заказы вторичного рынка.", "errors.proposal.failedToAccept": "Не удалось принять предложение", "errors.proposal.failedToCancel": "Не удалось отменить предложение", "errors.proposal.failedToPropose": "Не удалось создать предложение", "errors.proposal.insufficientBalance": "Недостаточно средств для создания предложения.", "errors.proposal.internalError": "Внутренняя ошибка при обработке предложения.", "errors.proposal.invalidArguments": "Неверные аргументы предложения", "errors.proposal.invalidProposedPrice": "Неверная предложенная цена.", "errors.proposal.mustBeHigherThanExisting": "Предложенная цена должна быть выше существующих предложений.", "errors.proposal.noActiveProposalFound": "Активное предложение не найдено.", "errors.proposal.onlySellerCanAcceptProposal": "Только продавец может принимать предложения.", "errors.proposal.orderMustBeActive": "Заказ должен быть активным для создания предложения.", "errors.proposal.orderNotFound": "Заказ не найден.", "errors.proposal.proposalNotFound": "Предложение не найдено.", "errors.proposal.proposalOnlyOnSellOrders": "Предложения можно делать только на заказы продажи.", "errors.proposal.sellerCannotPropose": "Продавцы не могут делать предложения на свои заказы.", "errors.proposal.userAlreadyHasActiveProposal": "У вас уже есть активное предложение для этого заказа.", "errors.telegram.botTokenNotConfigured": "Токен Telegram бота не настроен.", "errors.telegram.firebaseAuthError": "Произошла ошибка Firebase Auth.", "errors.telegram.iamPermissionError": "У сервисного аккаунта Firebase нет необходимых разрешений IAM для создания пользовательских токенов.", "errors.telegram.initDataRequired": "Требуется initData.", "errors.telegram.invalidTelegramData": "Неверные данные Telegram.", "errors.validation.botTokenRequired": "Требуется токен бота.", "errors.validation.invalidBotToken": "Неверный токен бота.", "errors.validation.invalidCollectionId": "Требуется корректный ID коллекции.", "errors.validation.invalidOrderId": "Требуется корректный ID заказа.", "errors.validation.invalidPrice": "Требуется корректная цена.", "errors.validation.invalidSecondaryMarketPrice": "Требуется корректная цена для вторичного рынка.", "errors.validation.ownedGiftIdRequired": "Требуется ID принадлежащего подарка.", "errors.validation.positiveAmountRequired": "{fieldName} должно быть больше 0.", "errors.validation.requiredField": "{field} обязательно для заполнения.", "errors.validation.userIdOrTgIdRequired": "Требуется userId или tgId.", "errors.validation.invalidUserId": "Требуется действительный ID пользователя.", "errors.validation.invalidGiftId": "Требуется действительный ID подарка.", "errors.withdrawal.amountAboveMaximum": "Сумма вывода не может превышать {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Сумма вывода должна быть не менее {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Сумма вывода превышает лимит за 24 часа. Вы можете вывести до {remainingLimit} TON. Лимит обновится в {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Сумма слишком мала после вычета комиссий.", "errors.withdrawal.calculationFailed": "Не удалось рассчитать статус вывода", "errors.withdrawal.insufficientAvailableBalance": "Недостаточно доступных средств для вывода.", "footer.marketplace": "Маркетплейс", "footer.myGifts": "Подарки", "footer.myOrders": "Заказы", "footer.myProfile": "Профиль", "freezePeriodStatus.expired": "Истёк", "freezePeriodStatus.freezePeriodEnded": "Период заморозки завершён", "freezePeriodStatus.freezePeriodNotStarted": "Период заморозки ещё не начался", "freezePeriodStatus.timeRemaining": "Осталось {days}д {hours}ч {minutes}м {seconds}с", "fulfillAndResellDrawer.availableBalance": "Доступный баланс:", "fulfillAndResellDrawer.cancel": "Отмена", "fulfillAndResellDrawer.fulfillAndResell": "Исполнить и перепродать", "fulfillAndResellDrawer.insufficientBalance": "Недостаточно средств для блокировки {amount} TON", "fulfillAndResellDrawer.lockAmount": "Сумма блокировки:", "fulfillAndResellDrawer.lockPercentage": "Процент блокировки:", "fulfillAndResellDrawer.processing": "Обработка...", "fulfillAndResellDrawer.resellInformation": "Информация о перепродаже заказа", "fulfillAndResellDrawer.resellPrice": "Цена перепродажи (TON)", "fulfillAndResellDrawer.successMessage": "Заказ успешно исполнен и выставлен на перепродажу!", "fulfillAndResellDrawer.title": "Исполнить и перепродать заказ", "giftInfoDrawer.claimGiftSteps": "Следуйте этим шагам, чтобы получить подарок от релейера", "giftInfoDrawer.claimYourGift": "Получить подарок", "giftInfoDrawer.pressMyBuyOrders": "Нажмите 'Мои заказы на покупку'", "giftInfoDrawer.selectYourOrder": "Выберите ваш заказ", "giftInfoDrawer.sendGiftSteps": "Следуйте этим шагам, чтобы отправить подарок релейеру", "giftInfoDrawer.sendGiftToRelayer": "Отправить подарок релейеру", "gifts.clickLoginToSeeGifts": "Нажмите кнопку входа через Telegram, чтобы увидеть свои подарки", "gifts.myGifts": "Мои подарки", "gifts.noGiftsFound": "Подарки не найдены", "gifts.orderInfo": "Информация о заказе", "gifts.sellGift": "Продать подарок", "gifts.youAreNotLoggedIn": "Вы не вошли в систему", "giftsPage.attachToOrder": "Привязать к заказу", "giftsPage.createSellOrder": "Создать заказ на продажу", "giftsPage.withdrawGift": "Вывести подарок", "header.deposit": "Депозит", "header.profile": "Профиль", "header.walletDisconnected": "Кошелек отключён", "header.withdraw": "Вывод", "insufficientBalance.topUp": "Пополнить баланс", "linkGiftToOrderDrawer.cancel": "Отмена", "linkGiftToOrderDrawer.linkGiftToOrder": "Привязать подарок к заказу", "linkGiftToOrderDrawer.linking": "Привязка...", "linkGiftToOrderDrawer.noEligibleOrders": "Подходящие заказы не найдены. Вам нужны заказы со статусом \"Создан\" или \"Оплачен\", где вы являетесь продавцом.", "linkGiftToOrderDrawer.selectOrderToLink": "Выберите заказ из коллекции {collectionName} для привязки этого подарка", "loginModal.authenticationRequired": "Требуется аутентификация", "loginModal.mustBeLoggedIn": "Вы должны войти в систему для выполнения этого действия.", "loginModal.signInWithTelegram": "Войти через Telegram", "loginModal.signingIn": "Вход...", "marketplace.activity.executedOrdersDescription": "Выполненные заказы будут отображены здесь", "marketplace.activity.noActivityFound": "Активность не найдена", "marketplace.activity.orderNumber": "Заказ #{number}", "marketplace.activity.viewOrder": "Посмотреть заказ", "marketplace.createOrder.availableBalance": "Доступный баланс:", "marketplace.createOrder.backdrop": "Фон", "marketplace.createOrder.buyOrderSubtitle": "Разместить заказ на покупку предмета", "marketplace.createOrder.cancel": "Отмена", "marketplace.createOrder.collection": "Коллекция", "marketplace.createOrder.create": "Создать", "marketplace.createOrder.createBuyOrder": "Создать заказ на покупку", "marketplace.createOrder.createSellOrder": "Создать заказ на продажу", "marketplace.createOrder.creating": "Создание...", "marketplace.createOrder.enterPrice": "Введите цену в TON", "marketplace.createOrder.failedToLoadGifts": "Не удалось загрузить доступные подарки", "marketplace.createOrder.fillRequiredFields": "Пожалуйста, заполните все обязательные поля", "marketplace.createOrder.insufficientAvailableBalance": "Недостаточно доступных средств", "marketplace.createOrder.insufficientBalance": "Недостаточно средств", "marketplace.createOrder.insufficientBalanceMessage": "Недостаточно средств для блокировки {amount} TON", "marketplace.createOrder.itemPriceLabel": "Цена предмета (TON)", "marketplace.createOrder.loadingAvailableGifts": "Загрузка доступных подарков...", "marketplace.createOrder.loadingConfiguration": "Загрузка конфигурации...", "marketplace.createOrder.lockPercentage": "Процент блокировки:", "marketplace.createOrder.marketCollectionDescription": "После создания заказа вам нужно будет отправить подарок релейеру для активации этого заказа.", "marketplace.createOrder.marketCollectionNotice": "Уведомление о рыночной коллекции", "marketplace.createOrder.maximumPrice": "Максимальная цена {amount} TON", "marketplace.createOrder.minimumPrice": "Минимальная цена {amount} TON", "marketplace.createOrder.noGiftsAvailable": "Нет доступных подарков для этой коллекции. Пожалуйста, сначала внесите подарок боту.", "marketplace.createOrder.orderCreatedSuccess": "Заказ успешно создан!", "marketplace.createOrder.orderInformation": "Информация о заказе", "marketplace.createOrder.price": "Цена", "marketplace.createOrder.priceFloorError": "Цена должна быть не менее {amount} TON (минимальная цена)", "marketplace.createOrder.selectCollection": "Выберите коллекцию...", "marketplace.createOrder.selectGift": "Выбрать подарок", "marketplace.createOrder.selectGiftButton": "Выбрать подарок ({count} доступно)", "marketplace.createOrder.selectGiftRequired": "Пожалуйста, выберите подарок для привязки к этому заказу", "marketplace.createOrder.selectGiftSubtitle": "Выберите подарок для привязки к заказу", "marketplace.createOrder.selectGiftToAttach": "Выбрать подарок для привязки", "marketplace.createOrder.selectedGift": "Выбрано: {giftName} #{giftSymbol}", "marketplace.createOrder.sellOrderSubtitle": "Выставить ваш предмет на продажу", "marketplace.resell.cancel": "Отмена", "marketplace.resell.createResaleOrder": "Создать заказ на перепродажу", "marketplace.resell.failedToCreateOrder": "Не удалось создать заказ на вторичном рынке", "marketplace.resell.importantNotice": "Важное уведомление", "marketplace.resell.importantNoticeDescription": "После установки цены перепродажи ваш заказ будет размещён на вторичном рынке. Другие пользователи смогут купить его по установленной вами цене.", "marketplace.resell.loadingYourOrders": "Загрузка ваших заказов...", "marketplace.resell.maximumCollateral": "Максимальный залог: {amount} TON", "marketplace.resell.minimumPrice": "Минимум: {minPrice} TON", "marketplace.resell.minimumTonPlaceholder": "Минимум {minPrice} TON", "marketplace.resell.noOrdersFoundToResell": "Заказы для перепродажи не найдены", "marketplace.resell.originalOwnerRoyalty": "Роялти первого владельца - {percentage}% ({amount} TON)", "marketplace.resell.originalPrice": "Первоначальная цена", "marketplace.resell.priceTooHighDescription": "Цена перепродажи не может превышать сумму залога ({amount} TON).", "marketplace.resell.priceTooHighTitle": "Цена слишком высока", "marketplace.resell.resalePriceTon": "Цена перепродажи (TON)", "marketplace.resell.resellMyOrder": "Перепродать мой заказ", "marketplace.resell.selectOrderToResell": "Выберите заказ, который вы купили, для перепродажи на вторичном рынке", "marketplace.resell.setResalePrice": "Установить цену перепродажи", "marketplace.resell.setResalePriceButton": "Установить цену перепродажи", "marketplace.resell.setResalePriceSubtitle": "Установите цену для перепродажи этого заказа на вторичном рынке", "marketplace.resell.settingPrice": "Установка цены...", "marketplace.resell.successMessage": "Заказ на перепродажу успешно создан!", "marketplace.resell.tooHigh": "✗ Слишком высоко", "marketplace.resell.tooLow": "✗ Слишком низко", "marketplace.resell.updateResaleOrder": "Обновить заказ на перепродажу", "marketplace.resell.valid": "✓ Действительно", "marketplace.resell.youWillReceive": "Вы получите: {amount} TON", "marketplace.tabs.activity": "Активность", "marketplace.tabs.buy": "Купить", "marketplace.tabs.sell": "Продать", "marketplaceFilters.allCollections": "Все коллекции", "marketplaceFilters.max": "Макс цена", "marketplaceFilters.min": "<PERSON><PERSON><PERSON> цена", "marketplaceFilters.newestFirst": "Сначала новые", "marketplaceFilters.oldestFirst": "Сначала старые", "marketplaceFilters.priceHighToLow": "Сначала дорогие", "marketplaceFilters.priceLowToHigh": "Сначала дешевые", "marketplaceFilters.sortBy": "Сортировка", "mock.message": "Тестовое сообщение", "notFound.redirecting": "Перенаправление...", "notFound.takingYouBackToMarketplace": "Возвращаем вас на маркетплейс", "nouns.confirmation": "Подтверждение", "nouns.description": "Описание", "nouns.email": "Email", "nouns.error": "Ошибка", "nouns.from": "От", "nouns.name": "Имя", "nouns.orderNumber": "Заказ №{number}", "nouns.password": "Пароль", "nouns.price": "Цена", "nouns.service": "Сервис", "orderActors.anonymousUser": "Анонимный пользователь", "orderActors.buyer": "Покупатель", "orderActors.noBuyerAssigned": "Покупатель не назначен", "orderActors.noRoleAssigned": "{role} не назначен", "orderActors.noSellerAssigned": "Продавец не назначен", "orderActors.orderActors": "Участники заказа", "orderActors.resseller": "Перепродавец", "orderActors.seller": "Продавец", "orderDeadlineTimer.deadline": "Крайний срок", "orderDeadlineTimer.giftWillBecomeTransferable": "Подарок скоро станет передаваемым", "orderDeadlineTimer.sellerMustSend": "Продавец должен отправить", "orderDeadlineTimer.sendOrLoseCollateral": "Отправить или потерять залог", "orderDeadlineTimer.waiting": "Ожидание", "orderDetails.content.action": "Действие", "orderDetails.content.buy": "Купить", "orderDetails.content.fulfill": "Выполнить", "orderDetails.content.insufficientBalance": "Недостаточно средств для выполнения этого действия", "orderDetails.content.share": "Поделиться", "orderDetails.content.showResellHistory": "Показать историю перепродаж", "orderDetails.fees.buyer": "Покупатель", "orderDetails.fees.collateral": "Залог", "orderDetails.fees.collateralDescription": "{buyerPercentage}% залог для покупателей. Заблокирован до выполнения заказа. Мгновенно возвращается, если заказ не выполнен.", "orderDetails.fees.deposited": "Внесено", "orderDetails.fees.feePaidBySeller": "Комиссия {feePercent}%. Оплачивается продавцом.", "orderDetails.fees.gift": "подарок", "orderDetails.fees.orderDetailsAndFees": "Детали заказа и комиссии", "orderDetails.fees.purchaseFee": "Комиссия за покупку", "orderDetails.fees.seller": "Продавец", "orderDetails.lastUpdate": "Последнее обновление", "orderDetailsActionButtons.close": "Закрыть", "orderDetailsActionButtons.processing": "Обработка...", "orderDetailsHeaderSection.unknownCollection": "Неизвестная коллекция", "orderDetailsProposalsSection.makeProposal": "Сделать предложение", "orderDetailsProposalsSection.priceProposals": "Ценовые предложения", "orderDetailsUserInfoSection.anonymousUser": "Анонимный пользователь", "orderDetailsUserInfoSection.loading": "Загрузка...", "orderPageClient.failedToLoadOrder": "Не удалось загрузить заказ", "orderPageClient.orderNotFound": "Заказ не найден", "orderPageClient.redirectingToHome": "Перенаправление на главную...", "orderStatus.active": "Активный", "orderStatus.cancelled": "Отменён", "orderStatus.created": "Создан", "orderStatus.fulfilled": "Выполнен", "orderStatus.giftSentToRelayer": "Отправлен боту", "orderStatus.paid": "Оплачен", "orderStatusUtils.active": "Активный", "orderStatusUtils.buyerDeadline": "Срок для покупателя", "orderStatusUtils.buyerMustClaimGiftOrLoseCollateral": "Покупатель должен забрать подарок или потеряет залог", "orderStatusUtils.cancelled": "Отменён", "orderStatusUtils.claimGiftFromRelayerOrLoseCollateral": "Заберите подарок у релейера или потеряете залог", "orderStatusUtils.created": "Создан", "orderStatusUtils.deadline": "Крайний срок", "orderStatusUtils.fulfilled": "Выполнен", "orderStatusUtils.giftSentToRelayer": "Отправлен боту", "orderStatusUtils.paid": "Оплачен", "orderStatusUtils.sellerDeadline": "Срок для продавца", "orderStatusUtils.sellerMustSend": "Продавец должен отправить", "orderStatusUtils.sellerMustSendGiftOrLoseCollateral": "Продавец должен отправить подарок или потеряет залог", "orderStatusUtils.sendGiftToRelayerOrLoseCollateral": "Отправьте подарок релейеру или потеряете залог", "orderStatusUtils.sendOrLoseCollateral": "Отправить или потерять залог", "orderStatusUtils.timeToClaimGift": "Время забрать подарок", "orderStatusUtils.timeToSendGift": "Время отправить подарок", "orderTraitsSection.giftTraits": "Характеристики подарка", "orders.cancelOrder.cancel": "Отменить", "orders.cancelOrder.cancelOrder": "Отменить заказ", "orders.cancelOrder.cancellationWarning": "Это действие нельзя отменить.", "orders.cancelOrder.cancelling": "Отмена...", "orders.cancelOrder.collateralLossDescription": "Вы потеряете {amount} TON залога. Это действие нельзя отменить.", "orders.cancelOrder.collateralLossWarning": "Вы потеряете {amount} TON залога.", "orders.cancelOrder.collateralLost": "Залог потерян", "orders.cancelOrder.confirmCancellation": "Вы уверены, что хотите отменить этот заказ?", "orders.cancelOrder.failedToCancelOrder": "Не удалось отменить заказ: {message}", "orders.cancelOrder.keepOrder": "Оставить заказ", "orders.cancelOrder.orderCancelledSuccessfully": "Заказ успешно отменён", "orders.cancelOrder.penaltyFeeDescription": "Если вы отмените этот заказ, будет взиматься штраф в размере {fee} TON с вашего баланса. Это действие нельзя отменить.", "orders.cancelOrder.resellerEarningsLoss": "Кроме того, вы потеряете {amount} TON в заработке от перепродажи.", "orders.cancelOrder.resellerEarningsWarning": "Как перепродавец, вы потеряете потенциальную прибыль.", "orders.cancelOrder.unexpectedError": "Произошла неожиданная ошибка", "orders.cancelOrder.warningPenaltyFee": "Внимание: будет взиматься штраф", "orders.clickLoginToSeeOrders": "Нажмите кнопку входа через Telegram, чтобы увидеть ваши заказы", "orders.noBuyOrdersFound": "Заказы на покупку не найдены", "orders.noSellOrdersFound": "Заказы на продажу не найдены", "orders.tabs.myBuyOrders": "Покупка ({count})", "orders.tabs.mySellOrders": "Продажа ({count})", "orders.userOrderCard.activateOrder": "Активировать заказ", "orders.userOrderCard.attachGift": "Привязать подарок", "orders.userOrderCard.getAGift": "Получить gift", "orders.userOrderCard.getCancelledGift": "Получить отменённый подарок", "orders.userOrderCard.resellThisOrder": "Перепродать этот заказ", "orders.userOrderCard.sendAGift": "Отправить gift", "orders.youAreNotLoggedIn": "Вы не вошли в систему", "priceProposalDrawer.cancel": "Отмена", "priceProposalDrawer.currentPrice": "Текущая цена", "priceProposalDrawer.info": "Предложите свою цену для этого заказа", "priceProposalDrawer.proposedPrice": "Предложенная цена", "priceProposalDrawer.savings": "Экономия", "priceProposalDrawer.submitProposal": "Отправить предложение", "priceProposalDrawer.submitting": "Отправка...", "priceProposalDrawer.title": "Ценовое предложение", "profile.form.displayName": "Отображаемое имя", "profile.form.editProfile": "Редактировать профиль", "profile.form.enterYourDisplayName": "Введите ваше отображаемое имя", "profile.form.failedToUpdateProfile": "Не удалось обновить профиль. Попробуйте снова.", "profile.form.nameIsRequired": "Имя обязательно", "profile.form.nameTooLong": "Имя должно быть меньше 50 символов", "profile.form.profileUpdatedSuccessfully": "Профиль успешно обновлён!", "profile.form.updateProfile": "Обновить профиль", "profile.form.updating": "Обновление...", "profile.main": "Главная", "profile.myTransactions": "Мои транзакции", "profile.referralSection.anonymous": "Анонимный", "profile.referralSection.failedToLoadReferrals": "Не удалось загрузить рефералов", "profile.referralSection.failedToShareReferralLink": "Не удалось поделиться реферальной ссылкой", "profile.referralSection.friends": "друзей", "profile.referralSection.joinMeOnMarketplace": "Присоединяйтесь ко мне на этом удивительном маркетплейсе и начните зарабатывать вознаграждения!", "profile.referralSection.joinTheMarketplace": "Присоединиться к маркетплейсу", "profile.referralSection.loadingReferralData": "Загрузка реферальных данных...", "profile.referralSection.name": "Имя", "profile.referralSection.ofTheirPurchaseFees": "от их комиссий за покупку", "profile.referralSection.points": "<PERSON><PERSON><PERSON><PERSON>", "profile.referralSection.potentialEarnings": "Потенциальный доход", "profile.referralSection.referralLinkSharedSuccessfully": "Реферальная ссылка успешно отправлена!", "profile.referralSection.referralProgram": "Реферальная программа", "profile.referralSection.referralRateDescription": "Вы зарабатываете {percentage}% от комиссии за покупку, когда ваши рефералы совершают покупки", "profile.referralSection.shareReferralLink": "Поделиться реферальной ссылкой", "profile.referralSection.shareTheLinkGetPoints": "Поделитесь ссылкой - получите очки за подарки!", "profile.referralSection.sharing": "Отправка...", "profile.referralSection.yourReferralRate": "Ваша реферальная ставка", "profile.referralSection.yourReferrals": "Ваши рефералы ({count})", "profile.settings.animatedCollections": "Анимированные коллекции", "profile.settings.animatedCollectionsDescription": "Включить анимированные превью коллекций и эффекты", "profile.settings.settings": "Настройки", "profile.socialLinks.followUs": "Подписывайтесь на нас", "profile.socialLinks.followUsOn": "Подписывайтесь на нас в {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "Пока нет транзакций", "profile.transactionHistory.emptyState.transactionHistoryDescription": "История ваших транзакций появится здесь, когда вы начнёте торговать на маркетплейсе", "profile.transactionHistory.header.beta": "БЕТА", "profile.transactionHistory.header.refresh": "Обновить", "profile.transactionHistory.header.transactionHistory": "История транзакций", "profile.transactionHistory.loadingState.loadingYourTransactions": "Загрузка ваших транзакций...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Загрузка дополнительных транзакций...", "profile.transactionHistory.pagination.reachedEndOfHistory": "Вы достигли конца истории транзакций", "profile.transactionHistory.table.amount": "Сумма", "profile.transactionHistory.table.date": "Дата", "profile.transactionHistory.table.description": "Описание", "profile.transactionHistory.table.type": "Тип", "profile.transactionHistory.transactionHistory": "История транзакций", "profile.userInfo.anonymousUser": "Анонимный пользователь", "profile.userInfo.availableBalance": "Доступный баланс", "profile.userInfo.lockedBalance": "Заблокированный баланс", "profile.userInfo.myPoints": "Мои очки", "profile.userInfo.profileInformation": "Информация профиля", "profile.userInfo.totalBalance": "О<PERSON><PERSON><PERSON> баланс", "proposalsTable.accept": "Принять", "proposalsTable.accepting": "Принятие...", "proposalsTable.cancel": "Отменить", "proposalsTable.cancelling": "Отмена...", "proposalsTable.loadMore": "Загрузить ещё", "proposalsTable.loadingMore": "Загрузка...", "proposalsTable.noProposals": "Нет предложений", "proposalsTable.priceProposals": "Ценовые предложения", "proposalsTable.proposalAcceptedSuccess": "Предложение успешно принято", "proposalsTable.proposalCancelledSuccess": "Предложение успешно отменено", "proposalsTable.proposalCreatedSuccess": "Предложение успешно создано", "proposalsTable.statusAccepted": "Принято", "proposalsTable.statusActive": "Активно", "proposalsTable.statusCancelled": "Отменено", "proposalsTable.yourProposal": "Ваше предложение", "purchase.buyer.giftReadyToClaim": "Подарок готов к получению от релейера.", "purchase.buyer.paymentCompletedNoFee": "Покупка успешна! Платеж завершен! {netAmountToSeller} TON переведено продавцу. {actionMessage}", "purchase.buyer.paymentCompletedWithFee": "Покупка успешна! Платеж завершен! {netAmountToSeller} TON переведено продавцу. Комиссия за покупку {totalFee} TON применена. {actionMessage}", "purchase.buyer.waitingForSeller": "Ожидание отправки подарка продавцом.", "purchase.buyer.withLockAndFee": "Покупка успешна! {lockedAmount} TON заблокировано ({lockPercentage}% от заказа {orderPrice} TON). Комиссия за покупку {totalFee} TON применена. {actionMessage}", "purchase.buyer.withLockNoFee": "Покупка успешна! {lockedAmount} TON заблокировано ({lockPercentage}% от заказа {orderPrice} TON). {actionMessage}", "purchase.feeApplied": "Комиссия за покупку {totalFee} TON применена.", "purchase.fundsLocked": "{lockedAmount} TON заблокировано ({lockPercentage}% от заказа {orderPrice} TON).", "purchase.paymentCompleted": "Платеж завершен! {netAmountToSeller} TON переведено продавцу.", "purchase.seller.canSendGift": "Теперь вы можете отправить подарок.", "purchase.seller.withLock": "Покупка успешна! {lockedAmount} TON заблокировано ({lockPercentage}% от заказа {orderPrice} TON). {actionMessage}", "purchase.successful": "Покупка успешна!", "resellTxHistory.buyer": "Покупатель", "resellTxHistory.close": "Закрыть", "resellTxHistory.executionPrice": "Цена исполнения", "resellTxHistory.failedToFetchHistory": "Не удалось загрузить историю", "resellTxHistory.loadingResellHistory": "Загрузка истории перепродаж...", "resellTxHistory.noResellTransactions": "Нет транзакций перепродажи", "resellTxHistory.resellHistory": "История перепродаж", "resellTxHistory.resellHistoryCount": "История перепродаж ({count})", "resellTxHistory.resellTransaction": "Транзакция перепродажи", "resellTxHistory.reseller": "Перепродавец", "resellTxHistory.showingEarnings": "Показываем доходы", "resellTxHistory.yourEarnings": "Ваши доходы", "secondaryMarketBadge.resell": "Перепродажа", "sellButtonComponent.buy": "Купить", "sellPriceDetails.marketFeesIncluded": "комиссии маркетплейса включены", "shareLink.checkOutOrder": "Посмотрите этот заказ в маркетплейсе!", "shareLink.failedToShare": "Не удалось поделиться ссылкой", "shareLink.orderIdNotAvailable": "ID заказа недоступен", "tonConnect.authenticating": "Аутентификация...", "tonConnect.connect": "Подключить", "tonConnect.connecting": "Подключение...", "tonConnect.disconnect": "Отключить", "transaction.description.cancellationCompensationFromBuyerCollateral": "Компенсация за отмену из залога покупателя ({amount} TON)", "transaction.description.cancellationPenaltyForBuyer": "Штраф за отмену для покупателя ({amount} TON залога)", "transaction.description.cancellationPenaltyForSeller": "Штраф за отмену для продавца ({amount} TON)", "transaction.description.collateralUnlockedDueToAdminCancellation": "Залог разблокирован из-за отмены администратором ({amount} TON)", "transaction.description.collateralUnlockedDueToBuyerCancellation": "Залог разблокирован из-за отмены покупателем ({amount} TON)", "transaction.description.collateralUnlockedDueToCancellation": "Залог разблокирован из-за отмены заказа ({amount} TON)", "transaction.description.collateralUnlockedDueToSellerCancellation": "Залог разблокирован из-за отмены продавцом ({amount} TON)", "transaction.description.depositFromTonWallet": "Депозит с TON кошелька (исходная сумма: {originalAmount} TON, после комиссий: {netAmount} TON)", "transaction.description.fixedCancellationFeePenalty": "Фиксированный штраф за отмену ({amount} TON)", "transaction.description.lockedCollateralForBuyer": "Заблокирован залог покупателя ({amount} TON, {percentage}% от заказа {orderPrice} TON)", "transaction.description.lockedCollateralForSeller": "Заблокирован залог продавца ({amount} TON, {percentage}% от заказа {orderPrice} TON)", "transaction.description.proposalCancellationFee": "Комиссия за отмену предложения ({amount} TON)", "transaction.description.proposalCollateralLock": "Блокировка залога предложения ({amount} TON)", "transaction.description.proposalCollateralRefund": "Возврат залога предложения ({amount} TON)", "transaction.description.referralFeeFromPurchase": "Реферальная комиссия с покупки ({amount} TON)", "transaction.description.resellFeeEarningsFromBuyerCancellation": "Доход с комиссии перепродажи от отмены покупателем ({amount} TON)", "transaction.description.saleCompletedForOrder": "Продажа завершена для заказа #{orderNumber} ({netAmount} TON нетто после комиссий)", "transaction.description.unknown": "Транзакция", "transaction.description.unlockedBuyerCollateralForCancelledOrder": "Разблокирован залог покупателя для отмененного заказа #{orderNumber} ({amount} TON)", "transaction.description.withdrawalToTonWallet": "Вывод в TON кошелек (брутто: {grossAmount} TON, нетто: {netAmount} TON, комиссия: {feeAmount} TON)", "transaction.type.buyLockCollateral": "Блокировка покупателя", "transaction.type.cancelationFee": "Комиссия отмены", "transaction.type.deposit": "Депозит", "transaction.type.referralFee": "Реферальная комиссия", "transaction.type.refund": "Возврат", "transaction.type.resellFeeEarnings": "Доход от перепродажи", "transaction.type.sellFulfillment": "Исполнение", "transaction.type.sellLockCollateral": "Блокировка продавца", "transaction.type.unlockCollateral": "Разблокировка", "transaction.type.withdraw": "Вывод", "unifiedGiftInfoDrawer.activateOrderSteps": "Отправьте подарок напрямую релейеру, затем вернитесь в приложение и привяжите соответствующий подарок к вашему заказу", "unifiedGiftInfoDrawer.activateYourOrder": "Активировать ваш заказ", "unifiedGiftInfoDrawer.attachGiftToOrder": "Нажмите кнопку \"Привязать подарок к этому заказу\" и выберите внесённый подарок", "unifiedGiftInfoDrawer.claimGiftSteps": "Перейдите к боту, выберите 'Мои подарки' и выберите конкретный подарок, который хотите вывести. Вы увидите соответствующий заказ для этого подарка. Затем перейдите к основному релейеру и напишите сообщение 'Получить подарок'", "unifiedGiftInfoDrawer.claimYourGift": "Получить ваш подарок", "unifiedGiftInfoDrawer.close": "Закрыть", "unifiedGiftInfoDrawer.confirmAndGoToRelayer": "Подтвердить и перейти к релейеру", "unifiedGiftInfoDrawer.confirmAndSendToRelayer": "Подтвердить и отправить релейеру", "unifiedGiftInfoDrawer.depositGiftToBot": "Внести подарок боту", "unifiedGiftInfoDrawer.getCancelledGift": "Получить отменённый подарок", "unifiedGiftInfoDrawer.getCancelledGiftSteps": "Перейдите к боту, выберите 'Мои подарки' и выберите подарок, связанный с отменённым заказом", "unifiedGiftInfoDrawer.goToBot": "Перейти к боту", "unifiedGiftInfoDrawer.goToRelayerToRetrieve": "Перейти к релейеру для получения", "unifiedGiftInfoDrawer.instructions": "Инструкции", "unifiedGiftInfoDrawer.openBot": "Открыть бота", "unifiedGiftInfoDrawer.pressMuyBuyOrders": "Нажмите кнопку \"Мои заказы на покупку\"", "unifiedGiftInfoDrawer.pressMySellOrders": "Нажмите кнопку \"Мои заказы на продажу\"", "unifiedGiftInfoDrawer.pressMySellOrdersCancelled": "Нажмите кнопку \"Мои подарки\"", "unifiedGiftInfoDrawer.pressMySellOrdersPaid": "Нажмите кнопку \"Мои заказы на продажу\" и выберите из группы \"Оплаченные заказы\"", "unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation": "Нажмите кнопку \"Мои заказы на продажу\" и выберите из группы \"Ожидают активации\"", "unifiedGiftInfoDrawer.selectOrderToActivate": "Выберите подарок, который вы хотите вывести, связанный с этим отменённым заказом", "unifiedGiftInfoDrawer.selectOrderToGet": "Выберите заказ, который вы хотите получить", "unifiedGiftInfoDrawer.selectOrderToSend": "Выберите заказ, который вы хотите отправить", "unifiedGiftInfoDrawer.sendGiftSteps": "Отправьте подарок напрямую релейеру, затем вернитесь в приложение и привяжите подарок к вашему заказу с помощью кнопки 'Привязать подарок'", "unifiedGiftInfoDrawer.sendGiftToRelayer": "Отправить подарок релейеру", "unifiedGiftInfoDrawer.sendGiftToRelayerToActivate": "Отправьте подарок к {relayerLink}, чтобы активировать этот заказ", "userOrderActionsSection.cancelOrder": "Отменить заказ", "userOrderActionsSection.createResaleOrder": "Создать заказ на перепродажу", "userOrderActionsSection.showResellHistory": "Показать историю перепродаж", "userOrderActionsSection.updateResaleOrder": "Обновить заказ на перепродажу", "userOrderDeadlineSection.giftWillBecomeTransferableSoon": "Подарок скоро станет доступным для передачи", "userOrderDeadlineSection.waiting": "Ожидание", "userOrderPricingSection.primaryPrice": "Основная цена", "userOrderPricingSection.secondaryMarketPrice": "Цена вторичного рынка", "userOrderSellerEarningsSection.earningsDescription": "Вы получите доходы от перепродажи после выполнения заказа. Эта сумма представляет ваши накопленные доходы от каждой перепродажи этого заказа на вторичном рынке.", "userOrderSellerEarningsSection.resaleEarnings": "Доходы от перепродажи", "userOrderSellerEarningsSection.totalEarningsFromResales": "Общий доход от перепродаж:", "userOrderStatusAlerts.freezePeriodActive": "Период заморозки активен", "userOrderStatusAlerts.freezePeriodDescription": "Предметы коллекции пока нельзя передавать. Дождитесь окончания периода заморозки.", "userOrderStatusAlerts.giftReady": "Подарок готов!", "userOrderStatusAlerts.giftReadyDescription": "Ваш подарок был отправлен релейеру. Пожалуйста, посетите бота, чтобы забрать свой подарок.", "userOrderStatusAlerts.giftRefundAvailable": "Возврат подарка доступен", "userOrderStatusAlerts.giftRefundDescription": "Перейдите к релейеру, чтобы вернуть свой подарок.", "userOrderStatusAlerts.openBotForRefund": "Открыть бота для возврата", "userOrderStatusAlerts.openBotToClaim": "Открыть бота, чтобы забрать", "userOrderStatusAlerts.readyToSend": "Готов к отправке", "userOrderStatusAlerts.readyToSendDescription": "Теперь вы можете отправить подарок релейеру.", "userOrderStatusAlerts.waitingForTransfer": "Ожидание передачи", "userOrderStatusAlerts.waitingForTransferDescription": "Дождитесь, пока предмет коллекции станет доступным для передачи.", "userOrderUserInfoSection.noUserAssigned": "Пока не назначен {role}", "welcomeModal.choosePreferredLanguage": "Выберите предпочитаемый язык для приложения", "welcomeModal.close": "Закрыть", "welcomeModal.earnRewards": "Зарабатывайте награды", "welcomeModal.failedToShareLink": "Не удалось поделиться реферальной ссылкой", "welcomeModal.iSharedIt": "Я поделился", "welcomeModal.linkSharedSuccessfully": "Реферальная ссылка успешно отправлена", "welcomeModal.selectLanguage": "Выберите язык", "welcomeModal.shareAppDescription": "Поделитесь этим приложением, чтобы получать до 5% дохода от торгов других пользователей и получать очки для бесплатной покупки подарков", "welcomeModal.shareReferralLink": "Поделиться реферальной ссылкой", "welcomeModal.shareText": "Присоединяйтесь ко мне на маркетплейсе и начните торговать подарками!", "welcomeModal.shareTitle": "Присоединяйтесь к маркетплейсу", "welcomeModal.sharing": "Отправка...", "welcomeModal.skip": "Пропустить", "withdrawDrawer.amountMustBeAtLeast": "Сумма должна быть не менее 1 TON", "withdrawDrawer.availableBalance": "Доступный баланс:", "withdrawDrawer.cancel": "Отмена", "withdrawDrawer.enterAmountToWithdraw": "Введите сумму для вывода", "withdrawDrawer.exceeds24HourLimit": "Превышен 24-часовой лимит. Остается: {remainingAmount} TON", "withdrawDrawer.insufficientAvailableBalance": "Недостаточно доступных средств", "withdrawDrawer.insufficientBalance": "Недостаточно доступных средств", "withdrawDrawer.invalidAmount": "Неверная сумма", "withdrawDrawer.invalidWithdrawalAmount": "Неверная сумма для вывода", "withdrawDrawer.limitResetsAt": "Лимит обновится в:", "withdrawDrawer.loadingConfiguration": "Загрузка конфигурации...", "withdrawDrawer.max": "Максимум", "withdrawDrawer.minTonPlaceholder": "<PERSON>ин {minAmount} TON", "withdrawDrawer.minimumWithdrawal": "Минимальный вывод:", "withdrawDrawer.minimumWithdrawalAmount": "Минимальная сумма вывода {minAmount} TON", "withdrawDrawer.netAmount": "Чистая сумма:", "withdrawDrawer.noWalletAddressFound": "Адрес кошелька не найден в вашем профиле", "withdrawDrawer.pleaseConnectWallet": "Пожалуйста, подключите кошелек для вывода средств", "withdrawDrawer.pleaseConnectWalletFirst": "Сначала подключите ваш кошелек", "withdrawDrawer.processing": "Обработка...", "withdrawDrawer.remainingLimit": "Оставшийся лимит:", "withdrawDrawer.unexpectedError": "Произошла неожиданная ошибка", "withdrawDrawer.withdraw": "Вывести", "withdrawDrawer.withdrawAmount": "Сумма вывода:", "withdrawDrawer.withdrawAmountTon": "Сумма вывода (TON)", "withdrawDrawer.withdrawFunds": "Вывести средства", "withdrawDrawer.withdrawTonToWallet": "Вывести TON на подключённый кошелек", "withdrawDrawer.withdrawalFailed": "Вывод не удался: {message}", "withdrawDrawer.withdrawalFee": "Комиссия за вывод:", "withdrawDrawer.withdrawalInformation": "Информация о выводе", "withdrawDrawer.withdrawalLimit24h": "Лимит вывода за 24 часа:", "withdrawDrawer.withdrawalSuccessful": "Вывод успешен! Транзакция: {hash}", "withdrawDrawer.youWillReceive": "Вы получите:", "withdrawGiftDrawer.close": "Закрыть", "withdrawGiftDrawer.instructionStep1": "Перейдите к боту, нажмите на \"Мои подарки\" и выберите подарок, который хотите вывести", "withdrawGiftDrawer.instructionStep2": "Затем перейдите к основному релейеру и напишите сообщение \"Получить подарок\"", "withdrawGiftDrawer.instructionStep3": "Перейдите к релейеру и напишите \"Получить мой подарок.\"", "withdrawGiftDrawer.openBot": "Открыть бота", "withdrawGiftDrawer.withdrawGift": "Вывести подарок", "withdrawGiftDrawer.withdrawInstructions": "1. Перейдите к боту, нажмите на 'Мои подарки' и выберите подарок, который хотите вывести 2. Затем перейдите к основному релейеру и напишите сообщение 'Получить подарок'", "sendGiftInstructions.title": "Инструкции по отправке подарка", "sendGiftInstructions.step1": "Перейдите к боту по ссылке - <relayerLink>@premrelayer</relayerLink>", "sendGiftInstructions.step2": "Убедитесь, что никнейм точно совпадает с указанным", "sendGiftInstructions.step3": "Отправьте подарок боту <relayerLink>@premrelayer</relayerLink>", "sendGiftInstructions.step4": "Нажмите на профиль - 3 точки - \"Отправить подарок\"", "sendGiftInstructions.step5": "Ваши подарки в полной безопасности и будут закреплены за вашим аккаунтом в PREM", "sendGiftInstructions.step6": "Подарок отобразится в течение минуты", "sendGiftInstructions.step7": "Если подарок не появился - обновите страницу", "sendGiftInstructions.close": "Закрыть", "sendGiftInstructions.openRelayer": "Открыть релейер"}