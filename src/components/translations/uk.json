{"actions.apply": "Застосувати", "actions.buy": "Купити", "actions.cancel": "Скасувати", "actions.close": "Закрити", "actions.confirm": "Підтвердити", "actions.delete": "Видалити", "actions.edit": "Редагувати", "actions.fulfill": "Виконати", "actions.pause": "Пауза", "actions.reject": "Від<PERSON><PERSON><PERSON>ити", "actions.resend": "Надіслати повторно", "actions.save": "Зберегти", "actions.saved": "Збережено", "actions.send": "Надіслати", "actions.sent": "Надіслано", "actions.signIn": "Увійти", "actions.signOut": "Вийти", "actions.signUp": "Зареєструватися", "actions.submit": "Відправити", "attachGiftToOrderDrawer.activateOrderTitle": "Активувати замовлення", "attachGiftToOrderDrawer.activateStep1": "1. Відправте подарунок безпосередньо релеєру", "attachGiftToOrderDrawer.activateStep2": "2. Поверніться в додаток", "attachGiftToOrderDrawer.activateStep3": "3. Натисніть кнопку 'Прив'язати подарунок'", "attachGiftToOrderDrawer.activateStep4": "4. О<PERSON><PERSON><PERSON><PERSON><PERSON>ь відповідний подарунок", "attachGiftToOrderDrawer.activateStep5": "5. Підтвердіть прив'язку", "attachGiftToOrderDrawer.errorLinkingGift": "Помилка прив'язки подарунка до замовлення", "attachGiftToOrderDrawer.errorLoadingGifts": "Помилка завантаження доступних подарунків", "attachGiftToOrderDrawer.giftLinkedSuccessfully": "Подарунок успішно прив'язано до замовлення", "attachGiftToOrderDrawer.instructionStep1": "Відправте подарунок безпосередньо релеєру, потім поверніться в додаток і прив'яжіть відповідний подарунок до вашого замовлення", "attachGiftToOrderDrawer.instructionStep2": "2. Натисніть кнопку \"Прив'язати подарунок до цього замовлення\" і оберіть внесений подарунок", "attachGiftToOrderDrawer.instructionsTitle": "Як прив'язати подарунок:", "attachGiftToOrderDrawer.linkGiftToOrder": "Прив'язати подарунок до замовлення", "attachGiftToOrderDrawer.linking": "Прив'язка...", "attachGiftToOrderDrawer.noGiftsAvailable": "Немає доступних подарунків для цієї колекції", "attachGiftToOrderDrawer.selectGift": "Оберіть подарунок для прив'язки", "attachGiftToOrderDrawer.title": "Прив'язати подарунок до замовлення", "collection.status.deleted": "Видалено", "collection.status.market": "Р<PERSON>н<PERSON><PERSON>", "collection.status.premarket": "Передпродаж", "collectionName.unknownCollection": "Невідома колекція", "collectionSelect.collection": "Колекція", "collectionSelect.noCollectionsFound": "Не знайдено колекцій, що відповідають \"{searchQuery}\".", "collectionSelect.searchCollections": "Пошук колекцій...", "collectionSelect.selectCollection": "Оберіть колекцію...", "common.failedToShare": "Не вдалося поділитися", "common.failedToShareReferralLink": "Не вдалося поділитися рефералкою", "common.linkCopiedToClipboard": "Посилання скопійовано до буфера обміну", "common.referralLinkCopiedToClipboard": "Реферальне посилання скопійовано до буфера обміну", "confirmWrapper.defaultMessage": "Ви впевнені, що хочете продовжити цю дію?", "confirmWrapper.no": "Ні", "confirmWrapper.yes": "Так", "countdownPopup.closeNotification": "Закрити сповіщення", "countdownPopup.depositProcessing": "Обробка депозиту", "countdownPopup.minutes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "countdownPopup.youWillReceiveFundsWithin": "Ви отримаєте свої кошти протягом", "createSellOrderDrawer.cancel": "Скасувати", "createSellOrderDrawer.createOrder": "Створити замовлення", "createSellOrderDrawer.createSellOrder": "Продати", "createSellOrderDrawer.creating": "Створення...", "createSellOrderDrawer.priceLabel": "Ц<PERSON>на (TON)", "createSellOrderDrawer.setPrice": "Встановіть ціну для вашого подарунка {giftName}", "createSellOrderDrawer.successMessage": "Замовлення на продаж успішно створено!", "depositDrawer.actions.cancel": "Скасувати", "depositDrawer.actions.deposit": "Депозит", "depositDrawer.actions.pleaseConnectWallet": "Будь ласка, підключіть свій гаманець для внесення депозиту", "depositDrawer.actions.processing": "Обробка...", "depositDrawer.addTonToBalance": "Додати TON до балансу маркетплейсу", "depositDrawer.amountInput.amountMustBeAtLeast": "Сума повинна бути не менше {amount} TON", "depositDrawer.amountInput.depositAmountTon": "Сума депозиту (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Мін {amount} TON", "depositDrawer.close": "Закрити", "depositDrawer.copyTransactionHash": "Скопіювати хеш транзакції", "depositDrawer.depositCompleted": "Ваш депозит успішно завершено", "depositDrawer.depositFee": "Комісія депозиту:", "depositDrawer.depositFunds": "Внести кошти", "depositDrawer.depositInformation": "Інформація про депозит", "depositDrawer.depositProcessing": "Обробка депозиту", "depositDrawer.depositSuccess": "Депозит успішний!", "depositDrawer.loadingConfiguration": "Завантаження конфігурації...", "depositDrawer.minimumDeposit": "Мінімальний депозит:", "depositDrawer.processingYourDeposit": "Обробка вашого депозиту...", "depositDrawer.summary.depositAmount": "Сума депозиту:", "depositDrawer.summary.depositFee": "Комісія депозиту:", "depositDrawer.summary.totalToPay": "Всього до сплати:", "depositDrawer.transactionHashCopied": "Хеш транзакції скопійовано до буфера обміну", "depositDrawer.viewOnTonScan": "Переглянути в TON Scan", "depositDrawer.youWillReceiveFundsWithin": "Ви отримаєте кошти протягом", "errorPage.tryAgain": "Спробувати знову", "errorPage.unhandledErrorOccurred": "Сталася необроблена помилка!", "errors.auth.adminOnly": "Тільки адміністратори можуть виконати цю операцію.", "errors.auth.permissionDenied": "Доступ заборонено.", "errors.auth.permissionDeniedWithOperation": "Ви можете виконати {operation} тільки для себе.", "errors.auth.telegramIdRequired": "Потрібен Telegram ID.", "errors.auth.tonWalletRequired": "У користувача не налаштовано адресу гаманця TON.", "errors.auth.unauthenticated": "Потрібна автентифікація.", "errors.auth.userNotFound": "Користувача не знайдено.", "errors.auth.walletAlreadyUsed": "Ця адреса гаманця вже використовується іншим користувачем.", "errors.balance.insufficientLockedFunds": "Недостатньо заблокованих коштів для цієї операції.", "errors.balance.insufficientLockedFundsToSpend": "Недостатньо заблокованих коштів для завершення цієї транзакції.", "errors.balance.insufficientLockedFundsToUnlock": "Недостатньо заблокованих коштів для розблокування запитаної суми.", "errors.fulfillAndResell.insufficientBalance": "Недостатньо коштів для створення замовлення на перепродаж.", "errors.fulfillAndResell.invalidOrderStatus": "Замовлення повинно мати статус \"подарунок відправлено релейеру\" для перепродажу.", "errors.fulfillAndResell.invalidParameters": "ID замовлення та ціна перепродажу обов'язкові та повинні бути дійсними.", "errors.fulfillAndResell.notOrderBuyer": "Ви не є покупцем цього замовлення.", "errors.fulfillAndResell.orderNotFound": "Замовлення не знайдено.", "errors.generic.authenticationFailed": "Помилка автентифікації.", "errors.generic.operationFailed": "Операція не вдалася. Спробуйте знову.", "errors.generic.serverError": "Сталася помилка сервера.", "errors.generic.unknownError": "Сталася невідома помилка.", "errors.gift.giftAlreadyLinked": "Подарунок вже прив'язаний до іншого замовлення.", "errors.gift.giftCollectionMismatch": "Подарунок і замовлення повинні бути з однієї колекції.", "errors.gift.giftInvalidStatus": "Подарунок повинен мати статус \"внесений\" для прив'язки.", "errors.gift.giftNotFound": "Подарунок не знайдено.", "errors.gift.giftNotOwnedByUser": "Подарунок не належить поточному користувачу.", "errors.order.buyerCannotPurchaseSameOrder": "Ви не можете купити той же заказ знову.", "errors.order.buyersCannotCreateMarketOrders": "Покупці не можуть створювати замовлення для ринкових колекцій. Тільки продавці можуть створювати замовлення для ринкових колекцій.", "errors.order.collectionNotActive": "Колекція не активна.", "errors.order.collectionNotFound": "Колекцію не знайдено.", "errors.order.insufficientBalance": "Недостатньо коштів.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Тільки поточний покупець може встановити ціну на вторинному ринку.", "errors.order.onlyPaidOrdersPurchasable": "На вторинному ринку можна купувати тільки замовлення зі статусом СПЛАЧЕНО.", "errors.order.onlyPaidOrdersSecondaryMarket": "На вторинному ринку можна розміщувати тільки замовлення зі статусом СПЛАЧЕНО.", "errors.order.orderMustBeGiftSentStatus": "Замовлення повинне мати статус 'подарунок відправлено релеєру' для завершення покупки.", "errors.order.orderMustBePaidStatus": "Замовлення повинне мати статус 'сплачено' для відправки подарунка релеєру.", "errors.order.orderMustHaveBuyerAndSeller": "У замовлення повинні бути і покупець, і продавець для розміщення на вторинному ринку.", "errors.order.orderNotAvailableSecondaryMarket": "Замовлення недоступне на вторинному ринку.", "errors.order.orderNotFound": "Замовлення не знайдено.", "errors.order.secondaryPriceBelowMinimum": "Ціна на вторинному ринку повинна бути не менше {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Ціна на вторинному ринку не може перевищувати загальну заставу {totalCollateral} TON (покупець: {buyerAmount} TON + продавець: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Продавець не може купити своє власне замовлення на вторинному ринку.", "errors.order.tooManyCreatedOrders": "У вас вже є 3 замовлення, які потрібно активувати. Будь ласка, активуйте існуючі замовлення перед створенням нових.", "errors.proposal.cannotProposeOnSecondaryMarket": "Неможливо робити пропозиції на замовлення вторинного ринку.", "errors.proposal.failedToAccept": "Не вдалося прийняти пропозицію. Спробуйте знову.", "errors.proposal.failedToCancel": "Не вдалося скасувати пропозицію. Спробуйте знову.", "errors.proposal.failedToPropose": "Не вдалося створити пропозицію. Спробуйте знову.", "errors.proposal.insufficientBalance": "Недостатньо коштів для створення пропозиції.", "errors.proposal.internalError": "Внутрішня помилка при обробці пропозиції.", "errors.proposal.invalidArguments": "Надано неправильні параметри пропозиції.", "errors.proposal.invalidProposedPrice": "Неправильна запропонована ціна.", "errors.proposal.mustBeHigherThanExisting": "Запропонована ціна повинна бути вищою за існуючі пропозиції.", "errors.proposal.noActiveProposalFound": "Активну пропозицію не знайдено.", "errors.proposal.onlySellerCanAcceptProposal": "Тільки продавець може приймати пропозиції.", "errors.proposal.orderMustBeActive": "Замовлення повинно бути активним для створення пропозиції.", "errors.proposal.orderNotFound": "Замовлення не знайдено.", "errors.proposal.proposalNotFound": "Пропозицію не знайдено.", "errors.proposal.proposalOnlyOnSellOrders": "Пропозиції можна робити тільки на замовлення продажу.", "errors.proposal.sellerCannotPropose": "Продавці не можуть робити пропозиції на свої замовлення.", "errors.proposal.userAlreadyHasActiveProposal": "У вас вже є активна пропозиція для цього замовлення.", "errors.telegram.botTokenNotConfigured": "Токен Telegram бота не налаштовано.", "errors.telegram.firebaseAuthError": "Сталася помилка Firebase Auth.", "errors.telegram.iamPermissionError": "У сервісного акаунта Firebase немає необхідних дозволів IAM для створення користувальницьких токенів.", "errors.telegram.initDataRequired": "Потрібні initData.", "errors.telegram.invalidTelegramData": "Неправильні дані Telegram.", "errors.validation.botTokenRequired": "Потрібен токен бота.", "errors.validation.invalidBotToken": "Неправильний токен бота.", "errors.validation.invalidCollectionId": "Потрібен правильний ID колекції.", "errors.validation.invalidOrderId": "Потрібен правильний ID замовлення.", "errors.validation.invalidPrice": "Потрібна правильна ціна.", "errors.validation.invalidSecondaryMarketPrice": "Потрібна правильна ціна для вторинного ринку.", "errors.validation.ownedGiftIdRequired": "Потрібен ID належного подарунка.", "errors.validation.positiveAmountRequired": "{fieldName} повинно бути більше 0.", "errors.validation.requiredField": "{field} обов'язкове для заповнення.", "errors.validation.userIdOrTgIdRequired": "Пот<PERSON><PERSON><PERSON><PERSON>н userId або tgId.", "errors.validation.invalidUserId": "Потрібен дійсний ID користувача.", "errors.validation.invalidGiftId": "Потрібен дійсний ID подарунка.", "errors.withdrawal.amountAboveMaximum": "Сума виведення не може перевищувати {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Сума виведення повинна бути не менше {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Сума виведення перевищує ліміт за 24 години. Ви можете вивести до {remainingLimit} TON. Ліміт оновиться в {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Сума занадто мала після вирахування комісій.", "errors.withdrawal.calculationFailed": "Не вдалося розрахувати статус виведення", "errors.withdrawal.insufficientAvailableBalance": "Недостатньо доступних коштів для виведення.", "footer.marketplace": "Маркетплейс", "footer.myGifts": "Подарунки", "footer.myOrders": "Замовлення", "footer.myProfile": "Профіль", "freezePeriodStatus.expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freezePeriodStatus.freezePeriodEnded": "Період заморозки завершено", "freezePeriodStatus.freezePeriodNotStarted": "Період заморозки ще не почався", "freezePeriodStatus.timeRemaining": "Залиши<PERSON><PERSON><PERSON>я {days}д {hours}г {minutes}хв {seconds}с", "fulfillAndResellDrawer.availableBalance": "Доступний баланс:", "fulfillAndResellDrawer.cancel": "Скасувати", "fulfillAndResellDrawer.fulfillAndResell": "Виконати та перепродати", "fulfillAndResellDrawer.insufficientBalance": "Недостатньо коштів для блокування {amount} TON", "fulfillAndResellDrawer.lockAmount": "Сума блокування:", "fulfillAndResellDrawer.lockPercentage": "Відсоток блокування:", "fulfillAndResellDrawer.processing": "Обробка...", "fulfillAndResellDrawer.resellInformation": "Інформація про перепродаж замовлення", "fulfillAndResellDrawer.resellPrice": "Ціна перепродажу (TON)", "fulfillAndResellDrawer.successMessage": "Замовлення виконано та створено замовлення на перепродаж успішно!", "fulfillAndResellDrawer.title": "Виконати та перепродати замовлення", "giftInfoDrawer.claimGiftSteps": "Виконайте ці кроки, щоб отримати подарунок від релеєра", "giftInfoDrawer.claimYourGift": "Отримати подарунок", "giftInfoDrawer.pressMyBuyOrders": "Натисніть 'Мої замовлення на покупку'", "giftInfoDrawer.selectYourOrder": "Оберіть ваше замовлення", "giftInfoDrawer.sendGiftSteps": "Виконайте ці кроки, щоб відправити подарунок релеєру", "giftInfoDrawer.sendGiftToRelayer": "Відправити подарунок релеєру", "gifts.clickLoginToSeeGifts": "Натисніть кнопку входу через Telegram, щоб побачити свої подарунки", "gifts.myGifts": "Мої подарунки", "gifts.noGiftsFound": "Подарунки не знайдено", "gifts.orderInfo": "Інформація", "gifts.sellGift": "Продати подарунок", "gifts.youAreNotLoggedIn": "Ви не увійшли в систему", "giftsPage.attachToOrder": "Додати до ордеру", "giftsPage.createSellOrder": "Продати", "giftsPage.withdrawGift": "Вивести", "header.deposit": "Депозит", "header.profile": "Профіль", "header.walletDisconnected": "Гаманець відключено", "header.withdraw": "Виведення", "insufficientBalance.topUp": "Поповнити баланс", "linkGiftToOrderDrawer.cancel": "Скасувати", "linkGiftToOrderDrawer.linkGiftToOrder": "Прив'язати подарунок до замовлення", "linkGiftToOrderDrawer.linking": "Прив'язка...", "linkGiftToOrderDrawer.noEligibleOrders": "Підходящі замовлення не знайдено. Вам потрібні замовлення зі статусом \"Створено\" або \"Оплачено\", де ви є продавцем.", "linkGiftToOrderDrawer.selectOrderToLink": "Виберіть замовлення з колекції {collectionName} для прив'язки цього подарунка", "loginModal.authenticationRequired": "Потрібна автентифікація", "loginModal.mustBeLoggedIn": "Ви повинні увійти в систему для виконання цієї дії.", "loginModal.signInWithTelegram": "Увійти через Telegram", "loginModal.signingIn": "Вхід...", "marketplace.activity.executedOrdersDescription": "Виконані замовлення будуть відображені тут", "marketplace.activity.noActivityFound": "Активність не знайдена", "marketplace.activity.orderNumber": "Замовлення #{number}", "marketplace.activity.viewOrder": "Переглянути замовлення", "marketplace.createOrder.availableBalance": "Доступний баланс:", "marketplace.createOrder.backdrop": "Фон", "marketplace.createOrder.buyOrderSubtitle": "Розмістити замовлення на покупку предмета", "marketplace.createOrder.cancel": "Скасувати", "marketplace.createOrder.collection": "Колекція", "marketplace.createOrder.create": "Створити", "marketplace.createOrder.createBuyOrder": "Купити", "marketplace.createOrder.createSellOrder": "Продати", "marketplace.createOrder.creating": "Створення...", "marketplace.createOrder.enterPrice": "Введіть ціну в TON", "marketplace.createOrder.failedToLoadGifts": "Не вдалося завантажити доступні подарунки", "marketplace.createOrder.fillRequiredFields": "Будь ласка, заповніть усі обов'язкові поля", "marketplace.createOrder.insufficientAvailableBalance": "Недостатньо доступних коштів", "marketplace.createOrder.insufficientBalance": "Недостатньо коштів", "marketplace.createOrder.insufficientBalanceMessage": "Недостатньо коштів для блокування {amount} TON", "marketplace.createOrder.itemPriceLabel": "Ціна предмета (TON)", "marketplace.createOrder.loadingAvailableGifts": "Завантаження доступних подарунків...", "marketplace.createOrder.loadingConfiguration": "Завантаження конфігурації...", "marketplace.createOrder.lockPercentage": "Відсоток блокування:", "marketplace.createOrder.marketCollectionDescription": "Після створення замовлення вам потрібно буде відправити подарунок релеєру для активації цього замовлення.", "marketplace.createOrder.marketCollectionNotice": "Повідомлення про ринкову колекцію", "marketplace.createOrder.maximumPrice": "Максима<PERSON>ьна ціна {amount} TON", "marketplace.createOrder.minimumPrice": "Міні<PERSON>альна ціна {amount} TON", "marketplace.createOrder.noGiftsAvailable": "Немає доступних подарунків для цієї колекції. Будь ласка, спочатку внесіть подарунок боту.", "marketplace.createOrder.orderCreatedSuccess": "Замовлення успішно створено!", "marketplace.createOrder.orderInformation": "Інформація про замовлення", "marketplace.createOrder.price": "Ціна", "marketplace.createOrder.priceFloorError": "Ціна повинна бути не менше {amount} TON (мінімальна ціна)", "marketplace.createOrder.selectCollection": "Оберіть колекцію...", "marketplace.createOrder.selectGift": "Обрати подарунок", "marketplace.createOrder.selectGiftButton": "Обрати подарунок ({count} доступно)", "marketplace.createOrder.selectGiftRequired": "Будь ласка, оберіть подарунок для прив'язки до цього замовлення", "marketplace.createOrder.selectGiftSubtitle": "Оберіть подарунок для прив'язки до замовлення", "marketplace.createOrder.selectGiftToAttach": "Обрати подарунок для прив'язки", "marketplace.createOrder.selectedGift": "Обрано: {giftName} #{giftSymbol}", "marketplace.createOrder.sellOrderSubtitle": "Виставити ваш предмет на продаж", "marketplace.resell.cancel": "Скасувати", "marketplace.resell.createResaleOrder": "Створити замовлення на перепродаж", "marketplace.resell.failedToCreateOrder": "Не вдалося створити замовлення на вторинному ринку", "marketplace.resell.importantNotice": "Важливе повідомлення", "marketplace.resell.importantNoticeDescription": "Після встановлення ціни перепродажу ваше замовлення буде розміщено на вторинному ринку. Інші користувачі зможуть купити його за встановленою вами ціною.", "marketplace.resell.loadingYourOrders": "Завантаження ваших замовлень...", "marketplace.resell.maximumCollateral": "Максимум: {amount} TON (сума застави)", "marketplace.resell.minimumPrice": "Мінімум: {minPrice} TON", "marketplace.resell.minimumTonPlaceholder": "Мінімум {minPrice} TON", "marketplace.resell.noOrdersFoundToResell": "Замовлення для перепродажу не знайдено", "marketplace.resell.originalOwnerRoyalty": "Роялті першого власника - {percentage}% ({amount} TON)", "marketplace.resell.originalPrice": "Початкова ціна", "marketplace.resell.priceTooHighDescription": "Ціна перепродажу не може перевищувати суму застави ({amount} TON).", "marketplace.resell.priceTooHighTitle": "Ціна занадто висока", "marketplace.resell.resalePriceTon": "Ціна перепродажу (TON)", "marketplace.resell.resellMyOrder": "Перепродати моє замовлення", "marketplace.resell.selectOrderToResell": "Оберіть замовлення, яке ви купили, для перепродажу на вторинному ринку", "marketplace.resell.setResalePrice": "Встановити ціну", "marketplace.resell.setResalePriceButton": "Встановити ціну", "marketplace.resell.setResalePriceSubtitle": "Встановіть ціну для перепродажу цього замовлення на вторинному ринку", "marketplace.resell.settingPrice": "Встановлення ціни...", "marketplace.resell.successMessage": "Замовлення на вторинному ринку успішно створено!", "marketplace.resell.tooHigh": "✗ Занадто висока", "marketplace.resell.tooLow": "✗ Занадто низька", "marketplace.resell.updateResaleOrder": "Оновити замовлення на перепродаж", "marketplace.resell.valid": "✓ Дійсна", "marketplace.resell.youWillReceive": "Ви отримаєте: {amount} TON", "marketplace.tabs.activity": "Активність", "marketplace.tabs.buy": "Купити", "marketplace.tabs.sell": "Продати", "marketplaceFilters.allCollections": "Всі колекції", "marketplaceFilters.max": "Макс ціна", "marketplaceFilters.min": "<PERSON><PERSON><PERSON>", "marketplaceFilters.newestFirst": "Спочатку нові", "marketplaceFilters.oldestFirst": "Спочатку старі", "marketplaceFilters.priceHighToLow": "Спочатку дорогі", "marketplaceFilters.priceLowToHigh": "Спочатку дешеві", "marketplaceFilters.sortBy": "Сортування", "mock.message": "Тестове повідомлення", "notFound.redirecting": "Перенаправлення...", "notFound.takingYouBackToMarketplace": "Повертаємо вас на маркетплейс", "nouns.confirmation": "Підтвердження", "nouns.description": "<PERSON><PERSON><PERSON><PERSON>", "nouns.email": "Email", "nouns.error": "Помилка", "nouns.from": "<PERSON>ід", "nouns.name": "Ім'я", "nouns.orderNumber": "Замовлення №{number}", "nouns.password": "Пароль", "nouns.price": "Ціна", "nouns.service": "Серв<PERSON>с", "orderActors.anonymousUser": "Анонімний користувач", "orderActors.buyer": "Покупець", "orderActors.noBuyerAssigned": "Покупець не призначений", "orderActors.noRoleAssigned": "{role} не призначено", "orderActors.noSellerAssigned": "Продавець не призначений", "orderActors.orderActors": "Учасники замовлення", "orderActors.resseller": "Перепродавець", "orderActors.seller": "Продавець", "orderDeadlineTimer.deadline": "Кінцевий термін", "orderDeadlineTimer.giftWillBecomeTransferable": "Подарунок незабаром стане передаваним", "orderDeadlineTimer.sellerMustSend": "Продавець повинен відправити", "orderDeadlineTimer.sendOrLoseCollateral": "Відправити або втратити заставу", "orderDeadlineTimer.waiting": "Очікування", "orderDetails.content.action": "Дія", "orderDetails.content.buy": "Купити", "orderDetails.content.fulfill": "Виконати", "orderDetails.content.insufficientBalance": "Недостатньо коштів для виконання цієї дії", "orderDetails.content.share": "Поділити<PERSON>я", "orderDetails.content.showResellHistory": "Показати історію перепродаж<PERSON>в", "orderDetails.fees.buyer": "Покупець", "orderDetails.fees.collateral": "Застава", "orderDetails.fees.collateralDescription": "{buyerPercentage}% застава для покупців. Заблоковано до виконання замовлення. Миттєво повертається, якщо замовлення не виконано.", "orderDetails.fees.deposited": "Внесено", "orderDetails.fees.feePaidBySeller": "Комісія {feePercent}%. Сплачується продавцем.", "orderDetails.fees.gift": "подарунок", "orderDetails.fees.orderDetailsAndFees": "Деталі замовлення та комісії", "orderDetails.fees.purchaseFee": "Комісія за покупку", "orderDetails.fees.seller": "Продавець", "orderDetails.lastUpdate": "Останнє оновлення", "orderDetailsActionButtons.close": "Закрити", "orderDetailsActionButtons.processing": "Обробка...", "orderDetailsHeaderSection.unknownCollection": "Невідома колекція", "orderDetailsProposalsSection.makeProposal": "Зробити пропозицію", "orderDetailsProposalsSection.priceProposals": "Цінові пропозиції", "orderDetailsUserInfoSection.anonymousUser": "Анонімний користувач", "orderDetailsUserInfoSection.loading": "Завантаження...", "orderPageClient.failedToLoadOrder": "Не вдалося завантажити замовлення", "orderPageClient.orderNotFound": "Замовлення не знайдено", "orderPageClient.redirectingToHome": "Перенаправлення на головну...", "orderStatus.active": "Активний", "orderStatus.cancelled": "Скасований", "orderStatus.created": "Створений", "orderStatus.fulfilled": "Виконаний", "orderStatus.giftSentToRelayer": "Відправлено боту", "orderStatus.paid": "Сплачений", "orderStatusUtils.active": "Активний", "orderStatusUtils.buyerDeadline": "Термін для покупця", "orderStatusUtils.buyerMustClaimGiftOrLoseCollateral": "Покупець повинен забрати подарунок або втратить заставу", "orderStatusUtils.cancelled": "Скасований", "orderStatusUtils.claimGiftFromRelayerOrLoseCollateral": "Заберіть подарунок у релеєра або втратите заставу", "orderStatusUtils.created": "Створений", "orderStatusUtils.deadline": "Кінцевий термін", "orderStatusUtils.fulfilled": "Виконаний", "orderStatusUtils.giftSentToRelayer": "Відправлено боту", "orderStatusUtils.paid": "Сплачений", "orderStatusUtils.sellerDeadline": "Термін для продавця", "orderStatusUtils.sellerMustSend": "Продавець повинен відправити", "orderStatusUtils.sellerMustSendGiftOrLoseCollateral": "Продавець повинен відправити подарунок або втратить заставу", "orderStatusUtils.sendGiftToRelayerOrLoseCollateral": "Відправте подарунок релеєру або втратите заставу", "orderStatusUtils.sendOrLoseCollateral": "Відправити або втратити заставу", "orderStatusUtils.timeToClaimGift": "<PERSON>а<PERSON> забрати подарунок", "orderStatusUtils.timeToSendGift": "Час відправити подарунок", "orderTraitsSection.giftTraits": "Характеристики подарунка", "orders.cancelOrder.cancel": "Скасувати", "orders.cancelOrder.cancelOrder": "Скасувати замовлення", "orders.cancelOrder.cancellationWarning": "Цю дію неможливо скасувати.", "orders.cancelOrder.cancelling": "Скасування...", "orders.cancelOrder.collateralLossDescription": "Ви втратите {amount} TON застави. Ця дія є остаточною і не може бути скасована.", "orders.cancelOrder.collateralLossWarning": "Ви втратите {amount} TON застави.", "orders.cancelOrder.collateralLost": "Застава втрачена", "orders.cancelOrder.confirmCancellation": "Ви впевнені, що хочете скасувати це замовлення?", "orders.cancelOrder.failedToCancelOrder": "Не вдалося скасувати замовлення: {message}", "orders.cancelOrder.keepOrder": "Залишити замовлення", "orders.cancelOrder.orderCancelledSuccessfully": "Замовлення успішно скасовано", "orders.cancelOrder.penaltyFeeDescription": "Якщо ви скасувате це замовлення, буде знято штрафну комісію у розмірі {fee} TON з вашого балансу. Ця дія є остаточною і не може бути скасована.", "orders.cancelOrder.resellerEarningsLoss": "Крім того, ви втратите {amount} TON у вигоді перепродавця.", "orders.cancelOrder.resellerEarningsWarning": "Як перепродавець, ви втратите потенційний прибуток.", "orders.cancelOrder.unexpectedError": "Сталася неочікувана помилка", "orders.cancelOrder.warningPenaltyFee": "Попередження: Буде застосовано штрафну комісію", "orders.clickLoginToSeeOrders": "Натисніть кнопку входу через Telegram, щоб побачити ваші замовлення", "orders.noBuyOrdersFound": "Замовлення на покупку не знайдено", "orders.noSellOrdersFound": "Замовлення на продаж не знайдено", "orders.tabs.myBuyOrders": "Покупка ({count})", "orders.tabs.mySellOrders": "Продаж ({count})", "orders.userOrderCard.activateOrder": "Активувати замовлення", "orders.userOrderCard.attachGift": "Прив'язати подарунок", "orders.userOrderCard.getAGift": "Отримати gift", "orders.userOrderCard.getCancelledGift": "Отримати скасований подарунок", "orders.userOrderCard.resellThisOrder": "Перепродати", "orders.userOrderCard.sendAGift": "Відправити gift", "orders.youAreNotLoggedIn": "Ви не увійшли в систему", "priceProposalDrawer.cancel": "Скасувати", "priceProposalDrawer.currentPrice": "Поточна ціна", "priceProposalDrawer.info": "Запропонуйте свою ціну для цього замовлення. Повна сума буде заблокована як застава до вирішення пропозиції.", "priceProposalDrawer.proposedPrice": "Запропонована ціна", "priceProposalDrawer.savings": "Економія {amount} TON ({percentage}%)", "priceProposalDrawer.submitProposal": "Відправити пропозицію", "priceProposalDrawer.submitting": "Відправлення...", "priceProposalDrawer.title": "Цінова пропозиція", "profile.form.displayName": "Відображуване ім'я", "profile.form.editProfile": "Редагувати профіль", "profile.form.enterYourDisplayName": "Введіть ваше відображуване ім'я", "profile.form.failedToUpdateProfile": "Не вдалося оновити профіль. Спробуйте знову.", "profile.form.nameIsRequired": "Ім'я обов'язкове", "profile.form.nameTooLong": "Ім'я повинно бути менше 50 символів", "profile.form.profileUpdatedSuccessfully": "Профіль успішно оновлено!", "profile.form.updateProfile": "Оновити профіль", "profile.form.updating": "Оновлення...", "profile.main": "Головна", "profile.myTransactions": "Мої транзакції", "profile.referralSection.anonymous": "Анонімний", "profile.referralSection.failedToLoadReferrals": "Не вдалося завантажити рефералів", "profile.referralSection.failedToShareReferralLink": "Не вдалося поділитися рефералкою", "profile.referralSection.friends": "дру<PERSON><PERSON>в", "profile.referralSection.joinMeOnMarketplace": "Приєднуйтеся до мене на цьому дивовижному маркетплейсі та почніть заробляти винагороди!", "profile.referralSection.joinTheMarketplace": "Приєднатися до маркетплейсу", "profile.referralSection.loadingReferralData": "Завантаження реферальних даних...", "profile.referralSection.name": "Ім'я", "profile.referralSection.ofTheirPurchaseFees": "від їхніх комісій за покупку", "profile.referralSection.points": "очок", "profile.referralSection.potentialEarnings": "Потенційний дохід", "profile.referralSection.referralLinkSharedSuccessfully": "Реферальне посилання успішно відправлено!", "profile.referralSection.referralProgram": "Реферальна програма", "profile.referralSection.referralRateDescription": "Ви заробляєте {percentage}% від комісії за покупку, коли ваші реферали здійснюють покупки", "profile.referralSection.shareReferralLink": "Поділитися рефералкою", "profile.referralSection.shareTheLinkGetPoints": "Поділіться посиланням - отримайте очки за подарунки!", "profile.referralSection.sharing": "Відправлення...", "profile.referralSection.yourReferralRate": "Ваша реферальна ставка", "profile.referralSection.yourReferrals": "Ваші реферали ({count})", "profile.settings.animatedCollections": "Анімовані колекції", "profile.settings.animatedCollectionsDescription": "Увімкнути анімовані превью колекцій та ефекти", "profile.settings.settings": "Налаштування", "profile.socialLinks.followUs": "Підписуйтеся на нас", "profile.socialLinks.followUsOn": "Підписуйтеся на нас у {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "Поки немає транзакцій", "profile.transactionHistory.emptyState.transactionHistoryDescription": "Історія ваших транзакцій з'явиться тут, коли ви почнете торгувати на маркетплейсі", "profile.transactionHistory.header.beta": "БЕТА", "profile.transactionHistory.header.refresh": "Оновити", "profile.transactionHistory.header.transactionHistory": "Історія транзакцій", "profile.transactionHistory.loadingState.loadingYourTransactions": "Завантаження ваших транзакцій...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Завантаження додаткових транзакцій...", "profile.transactionHistory.pagination.reachedEndOfHistory": "Ви досягли кінця історії транзакцій", "profile.transactionHistory.table.amount": "Сума", "profile.transactionHistory.table.date": "Дата", "profile.transactionHistory.table.description": "<PERSON><PERSON><PERSON><PERSON>", "profile.transactionHistory.table.type": "Тип", "profile.transactionHistory.transactionHistory": "Історія транзакцій", "profile.userInfo.anonymousUser": "Анонімний користувач", "profile.userInfo.availableBalance": "Доступний баланс", "profile.userInfo.lockedBalance": "Заблокований баланс", "profile.userInfo.myPoints": "Мої поінти", "profile.userInfo.profileInformation": "Інформація профілю", "profile.userInfo.totalBalance": "Загальний баланс", "proposalsTable.accept": "Прийняти", "proposalsTable.accepting": "Прийняття...", "proposalsTable.cancel": "Скасувати", "proposalsTable.cancelling": "Скасування...", "proposalsTable.loadMore": "Заванта<PERSON>ити більше", "proposalsTable.loadingMore": "Завантаження...", "proposalsTable.noProposals": "Немає пропозицій", "proposalsTable.priceProposals": "Цінові пропозиції", "proposalsTable.proposalAcceptedSuccess": "Цінова пропозиція прийнята успішно", "proposalsTable.proposalCancelledSuccess": "Цінова пропозиція скасована успішно", "proposalsTable.proposalCreatedSuccess": "Цінова пропозиція створена успішно", "proposalsTable.statusAccepted": "Прийнято", "proposalsTable.statusActive": "Активно", "proposalsTable.statusCancelled": "Скасовано", "proposalsTable.yourProposal": "Ваша пропозиція", "purchase.buyer.giftReadyToClaim": "Подарунок готовий до отримання від релейера.", "purchase.buyer.paymentCompletedNoFee": "Покупка успішна! Платіж завершено! {netAmountToSeller} TON переведено продавцю. {actionMessage}", "purchase.buyer.paymentCompletedWithFee": "Покупка успішна! Платіж завершено! {netAmountToSeller} TON переведено продавцю. Комісія за покупку {totalFee} TON застосована. {actionMessage}", "purchase.buyer.waitingForSeller": "Очікування відправки подарунка продавцем.", "purchase.buyer.withLockAndFee": "Покупка успішна! {lockedAmount} TON заблоковано ({lockPercentage}% від замовлення {orderPrice} TON). Комісія за покупку {totalFee} TON застосована. {actionMessage}", "purchase.buyer.withLockNoFee": "Покупка успішна! {lockedAmount} TON заблоковано ({lockPercentage}% від замовлення {orderPrice} TON). {actionMessage}", "purchase.feeApplied": "Комісія за покупку {totalFee} TON застосована.", "purchase.fundsLocked": "{lockedAmount} TON заблоковано ({lockPercentage}% від замовлення {orderPrice} TON).", "purchase.paymentCompleted": "Платіж завершено! {netAmountToSeller} TON переведено продавцю.", "purchase.seller.canSendGift": "Тепер ви можете відправити подарунок.", "purchase.seller.withLock": "Покупка успішна! {lockedAmount} TON заблоковано ({lockPercentage}% від замовлення {orderPrice} TON). {actionMessage}", "purchase.successful": "Покупка успішна!", "resellTxHistory.buyer": "Покупець", "resellTxHistory.close": "Закрити", "resellTxHistory.executionPrice": "Ціна виконання", "resellTxHistory.failedToFetchHistory": "Не вдалося завантажити історію", "resellTxHistory.loadingResellHistory": "Завантаження історії перепродажів...", "resellTxHistory.noResellTransactions": "Немає транзакцій перепродажу", "resellTxHistory.resellHistory": "Історія перепродажів", "resellTxHistory.resellHistoryCount": "Історія перепрода<PERSON><PERSON>в ({count})", "resellTxHistory.resellTransaction": "Транзакція перепродажу", "resellTxHistory.reseller": "Перепродавець", "resellTxHistory.showingEarnings": "Показуємо доходи", "resellTxHistory.yourEarnings": "Ваші доходи", "secondaryMarketBadge.resell": "Премаркет", "sellButtonComponent.buy": "Купити", "sellPriceDetails.marketFeesIncluded": "комісії маркетплейсу включено", "shareLink.checkOutOrder": "Подивіться на це замовлення!", "shareLink.failedToShare": "Не вдалося поділитися замовленням", "shareLink.orderIdNotAvailable": "ID замовлення недоступний", "tonConnect.authenticating": "Автентифікація...", "tonConnect.connect": "Підключити", "tonConnect.connecting": "Підключення...", "tonConnect.disconnect": "Відключити", "transaction.description.cancellationCompensationFromBuyerCollateral": "Компенсація за скасування з застави покупця ({amount} TON)", "transaction.description.cancellationPenaltyForBuyer": "Штраф за скасування для покупця ({amount} TON застави)", "transaction.description.cancellationPenaltyForSeller": "Штраф за скасування для продавця ({amount} TON)", "transaction.description.collateralUnlockedDueToAdminCancellation": "Застава розблокована через скасування адміністратором ({amount} TON)", "transaction.description.collateralUnlockedDueToBuyerCancellation": "Застава розблокована через скасування покупцем ({amount} TON)", "transaction.description.collateralUnlockedDueToCancellation": "Застава розблокована через скасування замовлення ({amount} TON)", "transaction.description.collateralUnlockedDueToSellerCancellation": "Застава розблокована через скасування продавцем ({amount} TON)", "transaction.description.depositFromTonWallet": "Депозит з TON гаманця (початкова сума: {originalAmount} TON, після комісій: {netAmount} TON)", "transaction.description.fixedCancellationFeePenalty": "Фіксований штраф за скасування ({amount} TON)", "transaction.description.lockedCollateralForBuyer": "Заблоковано заставу покупця ({amount} TON, {percentage}% від замовлення {orderPrice} TON)", "transaction.description.lockedCollateralForSeller": "Заблоковано заставу продавця ({amount} TON, {percentage}% від замовлення {orderPrice} TON)", "transaction.description.proposalCancellationFee": "Комісія за скасування цінової пропозиції ({amount} TON для замовлення #{orderId})", "transaction.description.proposalCollateralLock": "Заблокована застава для цінової пропозиції ({amount} TON для замовлення #{orderId})", "transaction.description.proposalCollateralRefund": "Повернення від скасованої цінової пропозиції ({amount} TON для замовлення #{orderId})", "transaction.description.referralFeeFromPurchase": "Реферальна комісія з покупки ({amount} TON)", "transaction.description.resellFeeEarningsFromBuyerCancellation": "Дохід з комісії перепродажу від скасування покупцем ({amount} TON)", "transaction.description.saleCompletedForOrder": "Продаж завершено для замовлення #{orderNumber} ({netAmount} TON нетто після комісій)", "transaction.description.unknown": "Транзакція", "transaction.description.unlockedBuyerCollateralForCancelledOrder": "Розблоковано заставу покупця для скасованого замовлення #{orderNumber} ({amount} TON)", "transaction.description.withdrawalToTonWallet": "Виведення в TON гаманець (брутто: {grossAmount} TON, нетто: {netAmount} TON, комісія: {feeAmount} TON)", "transaction.type.buyLockCollateral": "Блокування покупця", "transaction.type.cancelationFee": "Комісія скасування", "transaction.type.deposit": "Депозит", "transaction.type.referralFee": "Реферальна комісія", "transaction.type.refund": "Повернення", "transaction.type.resellFeeEarnings": "Дохід від перепродажу", "transaction.type.sellFulfillment": "Виконання", "transaction.type.sellLockCollateral": "Блокування продавця", "transaction.type.unlockCollateral": "Розблокування", "transaction.type.withdraw": "Виведення", "unifiedGiftInfoDrawer.activateOrderSteps": "Відправте подарунок безпосередньо релеєру, потім поверніться в додаток і прив'яжіть відповідний подарунок до вашого замовлення", "unifiedGiftInfoDrawer.activateYourOrder": "Активувати ваше замовлення", "unifiedGiftInfoDrawer.attachGiftToOrder": "Натисніть кнопку \"Прив'язати подарунок до цього замовлення\" і оберіть внесений подарунок", "unifiedGiftInfoDrawer.claimGiftSteps": "Перейдіть до бота, оберіть 'Мої подарунки' і оберіть конкретний подарунок, який хочете вивести. Ви побачите відповідне замовлення для цього подарунка. Потім перейдіть до основного релеєра і напишіть повідомлення 'Отримати подарунок'", "unifiedGiftInfoDrawer.claimYourGift": "Отримати ваш подарунок", "unifiedGiftInfoDrawer.close": "Закрити", "unifiedGiftInfoDrawer.confirmAndGoToRelayer": "Підтвердіть дію та перейдіть до {relayerLink}, щоб отримати ваш подарунок", "unifiedGiftInfoDrawer.confirmAndSendToRelayer": "Підтвердіть дію та відправте цей подарунок до {relayerLink}", "unifiedGiftInfoDrawer.depositGiftToBot": "Внесіть ваш подарунок боту, використовуючи кнопку \"Внести подарунок\"", "unifiedGiftInfoDrawer.getCancelledGift": "Отримати скасований подарунок", "unifiedGiftInfoDrawer.getCancelledGiftSteps": "Виконайте ці кроки, щоб отримати ваш скасований подарунок", "unifiedGiftInfoDrawer.goToBot": "Перейти до {botLink}", "unifiedGiftInfoDrawer.goToRelayerToRetrieve": "Перейдіть до \"Основного релеєра\" і напишіть \"Отримати мій подарунок\"", "unifiedGiftInfoDrawer.instructions": "Інструкції", "unifiedGiftInfoDrawer.openBot": "Відкрити бота", "unifiedGiftInfoDrawer.pressMuyBuyOrders": "Натисніть кнопку \"Мої замовлення на покупку\"", "unifiedGiftInfoDrawer.pressMySellOrders": "Натисніть кнопку \"Мої замовлення на продаж\"", "unifiedGiftInfoDrawer.pressMySellOrdersCancelled": "Натисніть кнопку \"Мої подарунки\"", "unifiedGiftInfoDrawer.pressMySellOrdersPaid": "Натисніть кнопку \"Мої замовлення на продаж\" і оберіть з групи \"Сплачені замовлення\"", "unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation": "Натисніть кнопку \"Мої замовлення на продаж\" і оберіть з групи \"Очікують активації\"", "unifiedGiftInfoDrawer.selectOrderToActivate": "Оберіть подарунок, який ви хочете вивести, пов'язаний з цим скасованим замовленням", "unifiedGiftInfoDrawer.selectOrderToGet": "Оберіть замовлення, яке ви хочете отримати", "unifiedGiftInfoDrawer.selectOrderToSend": "Оберіть замовлення, яке ви хочете відправити", "unifiedGiftInfoDrawer.sendGiftSteps": "Відправте подарунок безпосередньо релеєру, потім поверніться в додаток і прив'яжіть подарунок до вашого замовлення за допомогою кнопки 'Прив'язати подарунок'", "unifiedGiftInfoDrawer.sendGiftToRelayer": "Відправити подарунок релеєру", "unifiedGiftInfoDrawer.sendGiftToRelayerToActivate": "Відправте подарунок до {relayerLink}, щоб активувати це замовлення", "userOrderActionsSection.cancelOrder": "Скасувати замовлення", "userOrderActionsSection.createResaleOrder": "Створити замовлення на перепродаж", "userOrderActionsSection.showResellHistory": "Показати історію перепродаж<PERSON>в", "userOrderActionsSection.updateResaleOrder": "Оновити замовлення на перепродаж", "userOrderDeadlineSection.giftWillBecomeTransferableSoon": "Подарунок незабаром стане доступним для передачі", "userOrderDeadlineSection.waiting": "Очікування", "userOrderPricingSection.primaryPrice": "Основна ціна", "userOrderPricingSection.secondaryMarketPrice": "Ціна вторинного ринку", "userOrderSellerEarningsSection.earningsDescription": "Ви отримаєте доходи від перепродажу після виконання замовлення. Ця сума представляє ваші накопичені доходи від кожної перепродажі цього замовлення на вторинному ринку.", "userOrderSellerEarningsSection.resaleEarnings": "Доходи від перепродажу", "userOrderSellerEarningsSection.totalEarningsFromResales": "Загальний дохід від перепродаж<PERSON>в:", "userOrderStatusAlerts.freezePeriodActive": "Період заморозки активний", "userOrderStatusAlerts.freezePeriodDescription": "Предмети колекції поки не можна передавати. Дочекайтеся закінчення періоду заморозки.", "userOrderStatusAlerts.giftReady": "Подарунок готовий!", "userOrderStatusAlerts.giftReadyDescription": "Ваш подарунок було відправлено релеєру. Будь ласка, відвідайте бота, щоб забрати свій подарунок.", "userOrderStatusAlerts.giftRefundAvailable": "Повернення подарунка доступне", "userOrderStatusAlerts.giftRefundDescription": "Перейдіть до релеєра, щоб повернути свій подарунок.", "userOrderStatusAlerts.openBotForRefund": "Відкрити бота для повернення", "userOrderStatusAlerts.openBotToClaim": "Відкрити бота, щоб забрати", "userOrderStatusAlerts.readyToSend": "Готовий до відправки", "userOrderStatusAlerts.readyToSendDescription": "Тепер ви можете відправити подарунок релеєру.", "userOrderStatusAlerts.waitingForTransfer": "Очікування передачі", "userOrderStatusAlerts.waitingForTransferDescription": "Дочекайтеся, поки предмет колекції стане доступним для передачі.", "userOrderUserInfoSection.noUserAssigned": "Поки не призначено {role}", "welcomeModal.choosePreferredLanguage": "Оберіть бажану мову для додатку", "welcomeModal.close": "Закрити", "welcomeModal.earnRewards": "Заробляйте винагороди", "welcomeModal.failedToShareLink": "Не вдалося поділитися рефералкою", "welcomeModal.iSharedIt": "Я поділився", "welcomeModal.linkSharedSuccessfully": "Реферальне посилання успішно надіслано", "welcomeModal.selectLanguage": "Обе<PERSON><PERSON><PERSON>ь мову", "welcomeModal.shareAppDescription": "Запросіть друзів та отримуйте до 5% доходу від торгівлі кожного користувача та отримуйте очки для безкоштовної покупки подарунків", "welcomeModal.shareReferralLink": "Поділитися рефералкою", "welcomeModal.shareText": "Приєднуйтесь до мене на маркетплейсі та почніть торгувати подарунками!", "welcomeModal.shareTitle": "Приєднуйтесь до маркетплейсу", "welcomeModal.sharing": "Надсилання...", "welcomeModal.skip": "Пропустити", "withdrawDrawer.amountMustBeAtLeast": "Сума повинна бути не менше 1 TON", "withdrawDrawer.availableBalance": "Доступний баланс:", "withdrawDrawer.cancel": "Скасувати", "withdrawDrawer.enterAmountToWithdraw": "Введіть суму для виведення", "withdrawDrawer.exceeds24HourLimit": "Перевищено 24-годинний ліміт. Залишається: {remainingAmount} TON", "withdrawDrawer.insufficientAvailableBalance": "Недостатньо доступних коштів", "withdrawDrawer.insufficientBalance": "Недостатньо доступних коштів", "withdrawDrawer.invalidAmount": "Неправильна сума", "withdrawDrawer.invalidWithdrawalAmount": "Неправильна сума для виведення", "withdrawDrawer.limitResetsAt": "Ліміт оновиться в:", "withdrawDrawer.loadingConfiguration": "Завантаження конфігурації...", "withdrawDrawer.max": "Максимум", "withdrawDrawer.minTonPlaceholder": "<PERSON><PERSON><PERSON> {minAmount} TON", "withdrawDrawer.minimumWithdrawal": "Мінімальне виведення:", "withdrawDrawer.minimumWithdrawalAmount": "Мінімальна сума виведення {minAmount} TON", "withdrawDrawer.netAmount": "Чиста сума:", "withdrawDrawer.noWalletAddressFound": "Адресу гаманця не знайдено у вашому профілі", "withdrawDrawer.pleaseConnectWallet": "Будь ласка, підключіть гаманець для виведення коштів", "withdrawDrawer.pleaseConnectWalletFirst": "Спочатку підключіть ваш гаманець", "withdrawDrawer.processing": "Обробка...", "withdrawDrawer.remainingLimit": "Залишковий ліміт:", "withdrawDrawer.unexpectedError": "Сталася неочікувана помилка", "withdrawDrawer.withdraw": "Вивести", "withdrawDrawer.withdrawAmount": "Сума виведення:", "withdrawDrawer.withdrawAmountTon": "Сума виведення (TON)", "withdrawDrawer.withdrawFunds": "Вивести кошти", "withdrawDrawer.withdrawTonToWallet": "Вивести TON на підключений гаманець", "withdrawDrawer.withdrawalFailed": "Виведення не вдалося: {message}", "withdrawDrawer.withdrawalFee": "Комісія за виведення:", "withdrawDrawer.withdrawalInformation": "Інформація про виведення", "withdrawDrawer.withdrawalLimit24h": "Ліміт виведення за 24 години:", "withdrawDrawer.withdrawalSuccessful": "Виведення успішне! Транзакція: {hash}", "withdrawDrawer.youWillReceive": "Ви отримаєте:", "withdrawGiftDrawer.close": "Закрити", "withdrawGiftDrawer.instructionStep1": "Перейдіть до {botLink}, натисніть на \"Мої подарунки\" і оберіть подарунок, який хочете вивести", "withdrawGiftDrawer.instructionStep2": "Потім перейдіть до {relayerLink} і напишіть повідомлення \"Отримати подарунок\"", "withdrawGiftDrawer.instructionStep3": "Потім перейдіть до основного релеєра і напишіть повідомлення \"Отримати подарунок\"", "withdrawGiftDrawer.openBot": "Відкрити бота", "withdrawGiftDrawer.withdrawGift": "Вивести подарунок", "withdrawGiftDrawer.withdrawInstructions": "1. Перейдіть до бота, натисніть на 'Мої подарунки' і оберіть подарунок, який хочете вивести 2. Потім перейдіть до основного релеєра і напишіть повідомлення 'Отримати подарунок'", "sendGiftInstructions.title": "Інструкції з відправки подарунка", "sendGiftInstructions.step1": "Перейдіть в бота за посиланням - <relayerLink>@premrelayer</relayerLink>", "sendGiftInstructions.step2": "Перевірте, щоб нікнейм точно співпадав з вказаним", "sendGiftInstructions.step3": "Надішліть подарунок боту <relayerLink>@premrelayer</relayerLink>", "sendGiftInstructions.step4": "Натисніть на профіль - 3 точки - \"Відправити подарунок\"", "sendGiftInstructions.step5": "Ваші подарунки в повній безпеці і будуть закріплені за вашим аккаунтом у PREM", "sendGiftInstructions.step6": "Подарунок відобразиться протягом хвилини", "sendGiftInstructions.step7": "Якщо подарунок не з'явився - оновіть сторінку", "sendGiftInstructions.close": "Закрити", "sendGiftInstructions.openRelayer": "Відкрити релеєр"}