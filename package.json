{"name": "marketplace-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "intl": "node scripts/intl.js", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@lottiefiles/react-lottie-player": "^3.6.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@telegram-apps/sdk-react": "^3.3.6", "@telegram-apps/telegram-ui": "^2.1.9", "@ton/core": "^0.61.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.3.1", "@tonconnect/ui-react": "^2.1.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "eruda": "^3.4.3", "firebase": "^12.0.0", "firebaseui": "^6.1.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "next": "15.5.0", "pako": "^2.1.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-intl": "^7.1.11", "react-number-format": "^5.4.4", "readline": "^1.3.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "telegram": "^2.26.22", "use-debounce": "^10.0.5", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/canvas-confetti": "^1.9.0", "@types/crypto-js": "^4.2.2", "@types/node": "^24.1.0", "@types/pako": "^2.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-react-intl": "^0.4.1"}}